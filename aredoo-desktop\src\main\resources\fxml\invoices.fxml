<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" 
      fx:controller="com.aredoo.desktop.controller.InvoicesController"
      spacing="10" styleClass="form-container">

   <padding>
      <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
   </padding>

   <!-- Header -->
   <HBox alignment="CENTER_LEFT" spacing="10">
      <Label text="إدارة الفواتير" styleClass="section-title">
         <font>
            <Font name="Arial" size="18.0" />
         </font>
      </Label>
      
      <Region HBox.hgrow="ALWAYS"/>
      
      <Button text="فاتورة جديدة" onAction="#createNewInvoice" 
              styleClass="quick-action-button" prefWidth="120"/>
   </HBox>

   <!-- Search and Filter -->
   <HBox spacing="10" alignment="CENTER_LEFT">
      <Label text="البحث:">
         <font>
            <Font name="Arial" size="12.0" />
         </font>
      </Label>
      <TextField fx:id="searchField" promptText="رقم الفاتورة أو اسم العميل..." 
                prefWidth="200" styleClass="text-field-arabic"/>
      
      <Label text="من تاريخ:">
         <font>
            <Font name="Arial" size="12.0" />
         </font>
      </Label>
      <DatePicker fx:id="fromDatePicker" prefWidth="120"/>
      
      <Label text="إلى تاريخ:">
         <font>
            <Font name="Arial" size="12.0" />
         </font>
      </Label>
      <DatePicker fx:id="toDatePicker" prefWidth="120"/>
      
      <Label text="الحالة:">
         <font>
            <Font name="Arial" size="12.0" />
         </font>
      </Label>
      <ComboBox fx:id="statusComboBox" prefWidth="100">
         <items>
            <String fx:value="الكل"/>
            <String fx:value="مدفوعة"/>
            <String fx:value="معلقة"/>
            <String fx:value="ملغية"/>
         </items>
      </ComboBox>
      
      <Button text="بحث" onAction="#searchInvoices" styleClass="toolbar-button"/>
      <Button text="إعادة تعيين" onAction="#resetSearch" styleClass="button"/>
   </HBox>

   <!-- Invoices Table -->
   <TableView fx:id="invoicesTable" VBox.vgrow="ALWAYS">
      <columns>
         <TableColumn fx:id="invoiceNumberColumn" text="رقم الفاتورة" prefWidth="120"/>
         <TableColumn fx:id="invoiceDateColumn" text="التاريخ" prefWidth="100"/>
         <TableColumn fx:id="customerNameColumn" text="العميل" prefWidth="150"/>
         <TableColumn fx:id="totalAmountColumn" text="المبلغ الكلي" prefWidth="100"/>
         <TableColumn fx:id="paidAmountColumn" text="المبلغ المدفوع" prefWidth="100"/>
         <TableColumn fx:id="remainingAmountColumn" text="المتبقي" prefWidth="100"/>
         <TableColumn fx:id="statusColumn" text="الحالة" prefWidth="80"/>
         <TableColumn fx:id="paymentMethodColumn" text="طريقة الدفع" prefWidth="100"/>
         <TableColumn fx:id="actionsColumn" text="الإجراءات" prefWidth="150"/>
      </columns>
   </TableView>

   <!-- Summary Cards -->
   <HBox spacing="15">
      <VBox styleClass="stat-card" HBox.hgrow="ALWAYS">
         <Label text="إجمالي الفواتير" styleClass="stat-title"/>
         <Label fx:id="totalInvoicesLabel" text="0" styleClass="stat-value"/>
      </VBox>
      
      <VBox styleClass="stat-card" HBox.hgrow="ALWAYS">
         <Label text="إجمالي المبيعات" styleClass="stat-title"/>
         <Label fx:id="totalSalesLabel" text="0.00 د.ع" styleClass="stat-value"/>
      </VBox>
      
      <VBox styleClass="stat-card" HBox.hgrow="ALWAYS">
         <Label text="المبلغ المدفوع" styleClass="stat-title"/>
         <Label fx:id="totalPaidLabel" text="0.00 د.ع" styleClass="stat-value"/>
      </VBox>
      
      <VBox styleClass="stat-card alert-card" HBox.hgrow="ALWAYS">
         <Label text="المبلغ المتبقي" styleClass="stat-title"/>
         <Label fx:id="totalRemainingLabel" text="0.00 د.ع" styleClass="stat-value"/>
      </VBox>
   </HBox>

   <!-- Bottom Actions -->
   <HBox spacing="10" alignment="CENTER_LEFT">
      <Button text="عرض التفاصيل" onAction="#viewInvoiceDetails" 
              styleClass="toolbar-button" prefWidth="100"/>
      <Button text="طباعة" onAction="#printSelectedInvoice" 
              styleClass="button" prefWidth="80"/>
      <Button text="تحديث الدفع" onAction="#updatePayment" 
              styleClass="button" prefWidth="100"/>
      <Button text="إلغاء الفاتورة" onAction="#cancelInvoice" 
              styleClass="button" prefWidth="100"/>
      
      <Region HBox.hgrow="ALWAYS"/>
      
      <Button text="تصدير إلى Excel" onAction="#exportToExcel" 
              styleClass="button" prefWidth="120"/>
      
      <Label fx:id="statusLabel" text="جاهز">
         <font>
            <Font name="Arial" size="11.0" />
         </font>
      </Label>
   </HBox>

</VBox>
