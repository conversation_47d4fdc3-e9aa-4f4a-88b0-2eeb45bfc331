# أريدوو - تطبيق سطح المكتب
## Aredoo Desktop POS Application

### 📋 نظرة عامة | Overview

**أريدوو** هو نظام إدارة المبيعات والمخزون مصمم خصيصاً للشركات والمتاجر العربية. يوفر واجهة سهلة الاستخدام باللغة العربية مع دعم كامل للنصوص من اليمين إلى اليسار (RTL).

**Aredoo** is a Point of Sale and Inventory Management system designed specifically for Arabic businesses and stores. It provides an easy-to-use Arabic interface with full Right-to-Left (RTL) text support.

### ✨ المميزات | Features

- 🏪 **إدارة المنتجات** - إضافة وتعديل وحذف المنتجات مع التصنيفات
- 📊 **إدارة المخزون** - تتبع الكميات والتنبيهات عند نفاد المخزون
- 🧾 **نظام الفواتير** - إنشاء فواتير المبيعات والمشتريات
- 👥 **إدارة العملاء** - قاعدة بيانات العملاء والموردين
- 📈 **التقارير** - تقارير مفصلة للمبيعات والمخزون والأرباح
- 🔐 **نظام المستخدمين** - مستويات صلاحيات متعددة (مدير، كاشير)
- 🌐 **واجهة عربية** - دعم كامل للغة العربية و RTL
- 💾 **قاعدة بيانات محلية** - لا يتطلب اتصال بالإنترنت

### 🛠️ التقنيات المستخدمة | Technologies Used

- **Java 11+** - لغة البرمجة الأساسية
- **JavaFX** - واجهة المستخدم الرسومية
- **Spring Boot** - إطار العمل الأساسي
- **Spring Data JPA** - للتعامل مع قاعدة البيانات
- **H2 Database** - قاعدة بيانات محلية
- **Maven** - إدارة المشروع والتبعيات
- **BCrypt** - تشفير كلمات المرور

### 📋 المتطلبات | Requirements

- **Java 11** أو أحدث
- **Maven 3.6+** (اختياري - مضمن في المشروع)
- **Windows 10/11** (تم الاختبار عليه)

### 🚀 التشغيل السريع | Quick Start

#### الطريقة الأولى - ملف التشغيل المحسن (الأفضل):
```bash
# انقر مرتين على الملف أو شغله من سطر الأوامر
start-aredoo-desktop.bat
```

#### الطريقة الثانية - ملف التشغيل البسيط:
```bash
run-desktop.bat
```

#### الطريقة الثالثة - Maven (إذا كان متوفراً):
```bash
# بناء المشروع
mvn clean compile

# تشغيل التطبيق
mvn javafx:run
```

#### الطريقة الرابعة - Java مباشرة:
```bash
# تأكد من وجود جميع التبعيات في مجلد lib
java -Dfile.encoding=UTF-8 -cp "target/classes;lib/*" com.aredoo.desktop.AredooDesktopApplication
```

### 👤 بيانات الدخول الافتراضية | Default Login

| المستخدم | كلمة المرور | الصلاحية |
|----------|-------------|----------|
| admin    | 1234        | مدير     |
| cashier  | 1234        | كاشير    |

### 📁 هيكل المشروع | Project Structure

```
aredoo-desktop/
├── src/main/java/com/aredoo/desktop/
│   ├── AredooDesktopApplication.java    # الكلاس الرئيسي
│   ├── controller/                      # Controllers
│   │   ├── LoginController.java
│   │   ├── MainController.java
│   │   └── ProductsController.java
│   ├── model/                          # نماذج البيانات
│   │   ├── Product.java
│   │   ├── User.java
│   │   └── ...
│   ├── repository/                     # طبقة البيانات
│   │   ├── ProductRepository.java
│   │   └── ...
│   └── service/                        # الخدمات
│       └── UserSession.java
├── src/main/resources/
│   ├── fxml/                          # ملفات الواجهة
│   │   ├── login.fxml
│   │   ├── main.fxml
│   │   └── products.fxml
│   ├── css/                           # ملفات التصميم
│   │   └── arabic-style.css
│   └── application.properties         # إعدادات التطبيق
├── pom.xml                            # إعدادات Maven
└── run-desktop.bat                    # ملف التشغيل
```

### 🎯 الاستخدام | Usage

1. **تسجيل الدخول**: استخدم بيانات الدخول الافتراضية
2. **لوحة التحكم**: عرض الإحصائيات والإجراءات السريعة
3. **إدارة المنتجات**: إضافة وتعديل المنتجات والتصنيفات
4. **الفواتير**: إنشاء فواتير المبيعات والمشتريات
5. **التقارير**: عرض تقارير مفصلة للأعمال

### 🔧 التخصيص | Customization

يمكن تخصيص التطبيق من خلال:
- تعديل ملف `application.properties` للإعدادات
- تخصيص التصميم في `arabic-style.css`
- إضافة مميزات جديدة في الكود المصدري

### 🐛 الإبلاغ عن المشاكل | Bug Reports

إذا واجهت أي مشاكل، يرجى:
1. التأكد من تثبيت Java 11+
2. التحقق من ملفات السجل (logs)
3. إعادة تشغيل التطبيق

### 📄 الترخيص | License

هذا المشروع مفتوح المصدر ومتاح للاستخدام التجاري والشخصي.

### 🤝 المساهمة | Contributing

نرحب بالمساهمات! يمكنك:
- الإبلاغ عن الأخطاء
- اقتراح مميزات جديدة
- تحسين الكود
- ترجمة التطبيق لغات أخرى

---

**تطوير**: فريق أريدوو | **Developed by**: Aredoo Team  
**الإصدار**: 1.0.0 | **Version**: 1.0.0
