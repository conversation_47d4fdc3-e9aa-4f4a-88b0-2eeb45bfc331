<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" 
      fx:controller="com.aredoo.desktop.controller.InvoiceFormController"
      spacing="10" styleClass="form-container">

   <padding>
      <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
   </padding>

   <!-- Header -->
   <HBox alignment="CENTER_LEFT" spacing="15">
      <Label text="فاتورة مبيعات جديدة" styleClass="section-title">
         <font>
            <Font name="Arial" size="18.0" />
         </font>
      </Label>
      
      <Region HBox.hgrow="ALWAYS"/>
      
      <Label text="رقم الفاتورة:"/>
      <Label fx:id="invoiceNumberLabel" text="INV-001" styleClass="field-label"/>
      
      <Label text="التاريخ:"/>
      <Label fx:id="invoiceDateLabel" styleClass="field-label"/>
   </HBox>

   <!-- Customer Selection -->
   <HBox spacing="10" alignment="CENTER_LEFT">
      <Label text="العميل:" prefWidth="80">
         <font>
            <Font name="Arial" size="12.0" />
         </font>
      </Label>
      <ComboBox fx:id="customerComboBox" prefWidth="200" promptText="اختر العميل"/>
      <Button text="عميل جديد" onAction="#addNewCustomer" styleClass="button"/>
      
      <Region HBox.hgrow="ALWAYS"/>
      
      <Label text="طريقة الدفع:"/>
      <ComboBox fx:id="paymentMethodComboBox" prefWidth="120">
         <items>
            <String fx:value="نقداً"/>
            <String fx:value="آجل"/>
            <String fx:value="بطاقة"/>
         </items>
      </ComboBox>
   </HBox>

   <!-- Product Search and Add -->
   <HBox spacing="10" alignment="CENTER_LEFT" styleClass="toolbar">
      <Label text="إضافة منتج:"/>
      <TextField fx:id="productSearchField" promptText="ابحث بالاسم أو الكود..." 
                prefWidth="200" styleClass="text-field-arabic"/>
      <Button text="بحث" onAction="#searchProduct" styleClass="toolbar-button"/>
      
      <Separator orientation="VERTICAL"/>
      
      <Label text="الكمية:"/>
      <TextField fx:id="quantityField" text="1" prefWidth="60" styleClass="text-field-arabic"/>
      <Button text="إضافة" onAction="#addProductToInvoice" styleClass="quick-action-button"/>
   </HBox>

   <!-- Invoice Items Table -->
   <TableView fx:id="invoiceItemsTable" VBox.vgrow="ALWAYS">
      <columns>
         <TableColumn fx:id="itemCodeColumn" text="الكود" prefWidth="80"/>
         <TableColumn fx:id="itemNameColumn" text="اسم المنتج" prefWidth="200"/>
         <TableColumn fx:id="itemUnitColumn" text="الوحدة" prefWidth="60"/>
         <TableColumn fx:id="itemQuantityColumn" text="الكمية" prefWidth="80"/>
         <TableColumn fx:id="itemPriceColumn" text="السعر" prefWidth="80"/>
         <TableColumn fx:id="itemTotalColumn" text="المجموع" prefWidth="100"/>
         <TableColumn fx:id="itemActionsColumn" text="إجراءات" prefWidth="80"/>
      </columns>
   </TableView>

   <!-- Invoice Summary -->
   <HBox spacing="20">
      <VBox spacing="5" HBox.hgrow="ALWAYS">
         <Label text="ملاحظات:" styleClass="field-label"/>
         <TextArea fx:id="notesArea" prefRowCount="3" promptText="ملاحظات إضافية..."/>
      </VBox>
      
      <VBox spacing="10" prefWidth="250" styleClass="stat-card">
         <HBox>
            <Label text="المجموع الفرعي:" HBox.hgrow="ALWAYS"/>
            <Label fx:id="subtotalLabel" text="0.00 د.ع" styleClass="stat-value"/>
         </HBox>
         
         <HBox>
            <Label text="الخصم:" HBox.hgrow="ALWAYS"/>
            <TextField fx:id="discountField" text="0" prefWidth="80" styleClass="text-field-arabic"/>
            <Label text="د.ع"/>
         </HBox>
         
         <HBox>
            <Label text="الضريبة:" HBox.hgrow="ALWAYS"/>
            <TextField fx:id="taxField" text="0" prefWidth="80" styleClass="text-field-arabic"/>
            <Label text="%"/>
         </HBox>
         
         <Separator/>
         
         <HBox>
            <Label text="المجموع الكلي:" styleClass="stat-title" HBox.hgrow="ALWAYS">
               <font>
                  <Font name="Arial" size="14.0" />
               </font>
            </Label>
            <Label fx:id="totalLabel" text="0.00 د.ع" styleClass="stat-value">
               <font>
                  <Font name="Arial" size="16.0" />
               </font>
            </Label>
         </HBox>
      </VBox>
   </HBox>

   <!-- Action Buttons -->
   <HBox spacing="10" alignment="CENTER">
      <Button text="حفظ الفاتورة" onAction="#saveInvoice" 
              styleClass="quick-action-button" prefWidth="120"/>
      <Button text="طباعة" onAction="#printInvoice" 
              styleClass="toolbar-button" prefWidth="80"/>
      <Button text="إلغاء" onAction="#cancel" 
              styleClass="button" prefWidth="80"/>
   </HBox>

   <!-- Status Label -->
   <Label fx:id="statusLabel" text="جاهز" styleClass="status-label"/>

</VBox>
