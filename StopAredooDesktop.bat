@echo off
chcp 65001 > nul
title إيقاف أريدوو

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      إيقاف أريدوو                           ║
echo ║                   Stopping Aredoo                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🛑 إيقاف تطبيق أريدوو...
echo 🛑 Stopping Aredoo application...

REM إيقاف تطبيق سطح المكتب
taskkill /f /im "AredooDesktop.exe" >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم إيقاف تطبيق سطح المكتب
    echo ✅ Desktop application stopped
)

REM إيقاف خادم ASP.NET Core
taskkill /f /im "Aredoo.Server.exe" >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم إيقاف الخادم
    echo ✅ Server stopped
)

REM إيقاف عمليات dotnet المتعلقة بأريدوو
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq dotnet.exe" /fo csv ^| find "dotnet.exe"') do (
    taskkill /f /pid %%i >nul 2>&1
)

echo.
echo ✅ تم إيقاف جميع عمليات أريدوو
echo ✅ All Aredoo processes stopped
echo.

timeout /t 2 /nobreak >nul
