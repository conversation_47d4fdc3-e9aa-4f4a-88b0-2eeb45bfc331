package com.aredoo.desktop.controller;

import com.aredoo.desktop.AredooDesktopApplication;
import com.aredoo.desktop.model.User;
import com.aredoo.desktop.repository.UserRepository;
import com.aredoo.desktop.service.UserSession;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.stage.Stage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Controller;

import java.util.Optional;

@Controller
public class LoginController {

    @FXML private TextField usernameField;
    @FXML private PasswordField passwordField;
    @FXML private Button loginButton;
    @FXML private Label errorLabel;

    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private UserSession userSession;

    private Stage primaryStage;
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    public void setPrimaryStage(Stage primaryStage) {
        this.primaryStage = primaryStage;
    }

    @FXML
    private void initialize() {
        // Set up enter key handling
        usernameField.setOnAction(e -> passwordField.requestFocus());
        passwordField.setOnAction(e -> handleLogin());
        
        // Focus on username field
        usernameField.requestFocus();
    }

    @FXML
    private void handleLogin() {
        String username = usernameField.getText().trim();
        String password = passwordField.getText();

        // Clear previous error
        errorLabel.setVisible(false);

        // Validate input
        if (username.isEmpty() || password.isEmpty()) {
            showError("يرجى إدخال اسم المستخدم وكلمة المرور");
            return;
        }

        try {
            // Find user
            Optional<User> userOptional = userRepository.findByUsername(username);
            
            if (userOptional.isEmpty()) {
                showError("اسم المستخدم غير صحيح");
                return;
            }

            User user = userOptional.get();

            // Check if user is active
            if (!user.getIsActive()) {
                showError("هذا المستخدم غير مفعل");
                return;
            }

            // Verify password
            if (!passwordEncoder.matches(password, user.getPassword())) {
                showError("كلمة المرور غير صحيحة");
                return;
            }

            // Login successful
            userSession.setCurrentUser(user);
            openMainWindow();

        } catch (Exception e) {
            showError("خطأ في تسجيل الدخول: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void showError(String message) {
        errorLabel.setText(message);
        errorLabel.setVisible(true);
    }

    private void openMainWindow() {
        try {
            // Get Spring context from the application
            Object userData = primaryStage.getScene().getWindow().getProperties().get("spring.context");

            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/main.fxml"));
            // Set controller factory to use Spring context for dependency injection
            loader.setControllerFactory(clazz -> {
                try {
                    // Get Spring context from AredooDesktopApplication
                    return AredooDesktopApplication.getSpringContext().getBean(clazz);
                } catch (Exception e) {
                    try {
                        return clazz.getDeclaredConstructor().newInstance();
                    } catch (Exception ex) {
                        throw new RuntimeException(ex);
                    }
                }
            });

            Parent root = loader.load();
            Scene scene = new Scene(root, 1200, 800);
            scene.getStylesheets().add(getClass().getResource("/css/arabic-style.css").toExternalForm());

            primaryStage.setTitle("أريدوو - نظام إدارة المبيعات والمخزون");
            primaryStage.setScene(scene);
            primaryStage.setMaximized(true);
            primaryStage.centerOnScreen();

        } catch (Exception e) {
            showError("خطأ في فتح النافذة الرئيسية: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
