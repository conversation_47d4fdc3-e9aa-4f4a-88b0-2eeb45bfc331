{"Version": 1, "Hash": "iDM9V+TRGxDjtz+emjrsq5gZer0YUKXRDcZ+txWgDlA=", "Source": "Aredoo.Server", "BasePath": "_content/Aredoo.Server", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Aredoo.Server\\wwwroot", "Source": "Aredoo.Server", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\25wrlhmap0-j1wgh6mdca.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "index#[.{fingerprint=j1wgh6mdca}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fl8j0yecj5", "Integrity": "Q1rMoUlGhutqC43l/Swj21FGxwOnw1gAhxocbbGJodA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\index.html", "FileLength": 1421, "LastWriteTime": "2025-11-20T14:43:22+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\5lixpq0z40-pjnfkijmjf.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/pos#[.{fingerprint=pjnfkijmjf}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\pos.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xrj6uyu0ck", "Integrity": "Y9AAQFdpxbnxeOLCYmeDn/xDQvyT15WTuIgo2q+uaA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\pos.js", "FileLength": 7718, "LastWriteTime": "2025-11-20T14:43:22+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\8c7jydm8ty-iyy8yegl4k.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/auth#[.{fingerprint=iyy8yegl4k}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\auth.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "voushzzqiw", "Integrity": "X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\auth.js", "FileLength": 1135, "LastWriteTime": "2025-11-20T14:43:22+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\eoulc2yy7k-sb2z2q5wz2.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/suppliers#[.{fingerprint=sb2z2q5wz2}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\suppliers.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "79pdnt8t6b", "Integrity": "G4zjpvKHBa9FT9vBtheUVUNyGnh082JCUyN20sqgGgE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\suppliers.js", "FileLength": 2405, "LastWriteTime": "2025-11-20T14:43:22+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\ill7x5ozqf-hy3h9tabsi.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/customers#[.{fingerprint=hy3h9tabsi}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\customers.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nynguhmsve", "Integrity": "sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\customers.js", "FileLength": 2684, "LastWriteTime": "2025-11-20T14:43:22+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\j099mgtiqd-uxkkg3wsx6.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/purchases#[.{fingerprint=uxkkg3wsx6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\purchases.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nant1d1596", "Integrity": "L2DAn2hNrvcZKW4yRq71tVHjOcqPKCI67MD5j5IHvmI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\purchases.js", "FileLength": 3854, "LastWriteTime": "2025-11-20T14:43:22+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\kd4at48or8-kgsze9z630.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/settings#[.{fingerprint=kgsze9z630}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\settings.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9gj<PERSON><PERSON><PERSON>", "Integrity": "uc88cmSCVyx4BQE3QaHAfuGDykSaV72SH+Z0KBHEbvg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\settings.js", "FileLength": 13145, "LastWriteTime": "2025-11-20T14:43:22+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\pv27i78e2o-2cv1skr50a.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "print-invoice#[.{fingerprint=2cv1skr50a}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\print-invoice.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f16cn8vlan", "Integrity": "bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\print-invoice.html", "FileLength": 3987, "LastWriteTime": "2025-11-20T14:43:22+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\sif3j9439v-7ene3dnx54.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/invoices#[.{fingerprint=7ene3dnx54}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\invoices.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w8qy1uzoa1", "Integrity": "oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\invoices.js", "FileLength": 4915, "LastWriteTime": "2025-11-20T14:43:22+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\tghp5tkl3n-zdfn4todj4.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "css/style#[.{fingerprint=zdfn4todj4}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\css\\style.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "79geveex89", "Integrity": "y+C+NGyTs1Bmv9JKA0wuI+utf8buSUwkKfizsp7zRkg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\css\\style.css", "FileLength": 7604, "LastWriteTime": "2025-11-20T14:43:22+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\vkpingxf2f-q4k5ude2ax.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/categories#[.{fingerprint=q4k5ude2ax}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\categories.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3h5qqccxjn", "Integrity": "N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\categories.js", "FileLength": 2390, "LastWriteTime": "2025-11-20T14:43:22+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\wa9tzd9v0l-9n0jr0b026.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/products#[.{fingerprint=9n0jr0b026}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\products.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ou6rhzkhk7", "Integrity": "cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\products.js", "FileLength": 8284, "LastWriteTime": "2025-11-20T14:43:22+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\wg78tukmqu-1posqj9azf.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/app#[.{fingerprint=1posqj9azf}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "horooyit8m", "Integrity": "IeIe2aS54ONtnLY1VQrOEwcC51f9qysdgbQPQ55G4Mc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\app.js", "FileLength": 2100, "LastWriteTime": "2025-11-20T14:43:22+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\x62rvtqy4o-sw1pmnb1tp.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/expenses#[.{fingerprint=sw1pmnb1tp}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\expenses.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x7x4kbs8j0", "Integrity": "0x7sxXZp5/3Pq8eQxZvOOUu4iOcv28hKJy22PKDxe8A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\expenses.js", "FileLength": 3438, "LastWriteTime": "2025-11-20T14:43:22+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\x78a6k87cc-2zj8ofrn42.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/reports#[.{fingerprint=2zj8ofrn42}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\reports.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kvzwheefbe", "Integrity": "oMYbV5AVYI0xejYZoQHZnI3REekGeYg+bDNc3rpK6pk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\reports.js", "FileLength": 6774, "LastWriteTime": "2025-11-20T14:43:22+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\ziqm8tcidl-nqonojhrly.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "login#[.{fingerprint=nqonojhrly}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\login.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dp8os1n73s", "Integrity": "701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\login.html", "FileLength": 2051, "LastWriteTime": "2025-11-20T14:43:22+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\css\\style.css", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "css/style#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zdfn4todj4", "Integrity": "WXp+PkfnkqGq7laJogkMEMowOu0V2IUls8a0oTQzRtY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\style.css", "FileLength": 39958, "LastWriteTime": "2025-11-19T18:06:43+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\index.html", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "j1wgh6mdca", "Integrity": "Ny8I2y2ZbLFWevWn+RfX+Sd5w3y+xAvPm0oSn/7tiU4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 7002, "LastWriteTime": "2025-11-19T18:06:52+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\app.js", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1posqj9azf", "Integrity": "+/1x5VNLgxPunpkoS/EDShG/ly1rYma2k0yHNCclphs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\app.js", "FileLength": 6298, "LastWriteTime": "2025-11-18T15:11:51+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\auth.js", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/auth#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "iyy8yegl4k", "Integrity": "BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\auth.js", "FileLength": 3765, "LastWriteTime": "2025-11-13T15:25:05+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\categories.js", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/categories#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "q4k5ude2ax", "Integrity": "Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\categories.js", "FileLength": 10343, "LastWriteTime": "2025-11-13T13:53:33+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\customers.js", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/customers#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hy3h9tabsi", "Integrity": "tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\customers.js", "FileLength": 14539, "LastWriteTime": "2025-11-13T13:52:53+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\expenses.js", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/expenses#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sw1pmnb1tp", "Integrity": "FKgwCz13SOOZVbBKGsOlK1NphH4brqQY0N1fqBQ8QIs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\expenses.js", "FileLength": 18166, "LastWriteTime": "2025-11-18T15:01:09+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\invoices.js", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/invoices#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7ene3dnx54", "Integrity": "71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\invoices.js", "FileLength": 30213, "LastWriteTime": "2025-11-12T19:27:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\pos.js", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/pos#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pjnfkijmjf", "Integrity": "9fBx2HYGq8pnWX4y638wJhSf2lSjcp8kaLo0vbBXo6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\pos.js", "FileLength": 37680, "LastWriteTime": "2025-11-19T18:06:29+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\products.js", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/products#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9n0jr0b026", "Integrity": "+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\products.js", "FileLength": 44477, "LastWriteTime": "2025-11-15T18:35:17+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\purchases.js", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/purchases#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "uxkkg3wsx6", "Integrity": "/lz99RppGma8f0Q9M/FfxUZAZyuStL95Lx7uSeAikc8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\purchases.js", "FileLength": 19719, "LastWriteTime": "2025-11-18T15:42:21+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\reports.js", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/reports#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2zj8ofrn42", "Integrity": "4sSIKiYZB5nuL7KWTQ1ImL4vKWYy7+KL+TfYKCHThQQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\reports.js", "FileLength": 57134, "LastWriteTime": "2025-11-18T14:05:57+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\settings.js", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/settings#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kgsze9z630", "Integrity": "LEPouUi0qenCXl3Oxe6EXY+crHhr0tJljs8+eaVn9KU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\settings.js", "FileLength": 90954, "LastWriteTime": "2025-11-19T17:40:28+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\suppliers.js", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/suppliers#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sb2z2q5wz2", "Integrity": "eM9g8qJqseSo9j4vYUbEBrdPIVvXd/JVkK87clenhso=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\suppliers.js", "FileLength": 10417, "LastWriteTime": "2025-11-18T15:30:17+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\login.html", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "login#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\login.html", "FileLength": 6732, "LastWriteTime": "2025-11-14T15:38:28+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\print-invoice.html", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "print-invoice#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2cv1skr50a", "Integrity": "4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\print-invoice.html", "FileLength": 17980, "LastWriteTime": "2025-11-13T19:09:19+00:00"}], "Endpoints": [{"Route": "css/style.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\tghp5tkl3n-zdfn4todj4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000131492439"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7604"}, {"Name": "ETag", "Value": "\"y+C+NGyTs1Bmv9JKA0wuI+utf8buSUwkKfizsp7zRkg=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"WXp+PkfnkqGq7laJogkMEMowOu0V2IUls8a0oTQzRtY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WXp+PkfnkqGq7laJogkMEMowOu0V2IUls8a0oTQzRtY="}]}, {"Route": "css/style.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\css\\style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "39958"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"WXp+PkfnkqGq7laJogkMEMowOu0V2IUls8a0oTQzRtY=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:06:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WXp+PkfnkqGq7laJogkMEMowOu0V2IUls8a0oTQzRtY="}]}, {"Route": "css/style.css.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\tghp5tkl3n-zdfn4todj4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7604"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"y+C+NGyTs1Bmv9JKA0wuI+utf8buSUwkKfizsp7zRkg=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y+C+NGyTs1Bmv9JKA0wuI+utf8buSUwkKfizsp7zRkg="}]}, {"Route": "css/style.zdfn4todj4.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\tghp5tkl3n-zdfn4todj4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000131492439"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7604"}, {"Name": "ETag", "Value": "\"y+C+NGyTs1Bmv9JKA0wuI+utf8buSUwkKfizsp7zRkg=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"WXp+PkfnkqGq7laJogkMEMowOu0V2IUls8a0oTQzRtY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zdfn4todj4"}, {"Name": "label", "Value": "css/style.css"}, {"Name": "integrity", "Value": "sha256-WXp+PkfnkqGq7laJogkMEMowOu0V2IUls8a0oTQzRtY="}]}, {"Route": "css/style.zdfn4todj4.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\css\\style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "39958"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"WXp+PkfnkqGq7laJogkMEMowOu0V2IUls8a0oTQzRtY=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:06:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zdfn4todj4"}, {"Name": "label", "Value": "css/style.css"}, {"Name": "integrity", "Value": "sha256-WXp+PkfnkqGq7laJogkMEMowOu0V2IUls8a0oTQzRtY="}]}, {"Route": "css/style.zdfn4todj4.css.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\tghp5tkl3n-zdfn4todj4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7604"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"y+C+NGyTs1Bmv9JKA0wuI+utf8buSUwkKfizsp7zRkg=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zdfn4todj4"}, {"Name": "label", "Value": "css/style.css.gz"}, {"Name": "integrity", "Value": "sha256-y+C+NGyTs1Bmv9JKA0wuI+utf8buSUwkKfizsp7zRkg="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\25wrlhmap0-j1wgh6mdca.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000703234880"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1421"}, {"Name": "ETag", "Value": "\"Q1rMoUlGhutqC43l/Swj21FGxwOnw1gAhxocbbGJodA=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"Ny8I2y2ZbLFWevWn+RfX+Sd5w3y+xAvPm0oSn/7tiU4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ny8I2y2ZbLFWevWn+RfX+Sd5w3y+xAvPm0oSn/7tiU4="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7002"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Ny8I2y2ZbLFWevWn+RfX+Sd5w3y+xAvPm0oSn/7tiU4=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:06:52 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ny8I2y2ZbLFWevWn+RfX+Sd5w3y+xAvPm0oSn/7tiU4="}]}, {"Route": "index.html.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\25wrlhmap0-j1wgh6mdca.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1421"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Q1rMoUlGhutqC43l/Swj21FGxwOnw1gAhxocbbGJodA=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Q1rMoUlGhutqC43l/Swj21FGxwOnw1gAhxocbbGJodA="}]}, {"Route": "index.j1wgh6mdca.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\25wrlhmap0-j1wgh6mdca.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000703234880"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1421"}, {"Name": "ETag", "Value": "\"Q1rMoUlGhutqC43l/Swj21FGxwOnw1gAhxocbbGJodA=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"Ny8I2y2ZbLFWevWn+RfX+Sd5w3y+xAvPm0oSn/7tiU4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j1wgh6mdca"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-Ny8I2y2ZbLFWevWn+RfX+Sd5w3y+xAvPm0oSn/7tiU4="}]}, {"Route": "index.j1wgh6mdca.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7002"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Ny8I2y2ZbLFWevWn+RfX+Sd5w3y+xAvPm0oSn/7tiU4=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:06:52 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j1wgh6mdca"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-Ny8I2y2ZbLFWevWn+RfX+Sd5w3y+xAvPm0oSn/7tiU4="}]}, {"Route": "index.j1wgh6mdca.html.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\25wrlhmap0-j1wgh6mdca.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1421"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Q1rMoUlGhutqC43l/Swj21FGxwOnw1gAhxocbbGJodA=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j1wgh6mdca"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-Q1rMoUlGhutqC43l/Swj21FGxwOnw1gAhxocbbGJodA="}]}, {"Route": "js/app.1posqj9azf.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\wg78tukmqu-1posqj9azf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000475963827"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2100"}, {"Name": "ETag", "Value": "\"IeIe2aS54ONtnLY1VQrOEwcC51f9qysdgbQPQ55G4Mc=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"+/1x5VNLgxPunpkoS/EDShG/ly1rYma2k0yHNCclphs=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1posqj9azf"}, {"Name": "label", "Value": "js/app.js"}, {"Name": "integrity", "Value": "sha256-+/1x5VNLgxPunpkoS/EDShG/ly1rYma2k0yHNCclphs="}]}, {"Route": "js/app.1posqj9azf.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6298"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+/1x5VNLgxPunpkoS/EDShG/ly1rYma2k0yHNCclphs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Nov 2025 15:11:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1posqj9azf"}, {"Name": "label", "Value": "js/app.js"}, {"Name": "integrity", "Value": "sha256-+/1x5VNLgxPunpkoS/EDShG/ly1rYma2k0yHNCclphs="}]}, {"Route": "js/app.1posqj9azf.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\wg78tukmqu-1posqj9azf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2100"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IeIe2aS54ONtnLY1VQrOEwcC51f9qysdgbQPQ55G4Mc=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1posqj9azf"}, {"Name": "label", "Value": "js/app.js.gz"}, {"Name": "integrity", "Value": "sha256-IeIe2aS54ONtnLY1VQrOEwcC51f9qysdgbQPQ55G4Mc="}]}, {"Route": "js/app.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\wg78tukmqu-1posqj9azf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000475963827"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2100"}, {"Name": "ETag", "Value": "\"IeIe2aS54ONtnLY1VQrOEwcC51f9qysdgbQPQ55G4Mc=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"+/1x5VNLgxPunpkoS/EDShG/ly1rYma2k0yHNCclphs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+/1x5VNLgxPunpkoS/EDShG/ly1rYma2k0yHNCclphs="}]}, {"Route": "js/app.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6298"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+/1x5VNLgxPunpkoS/EDShG/ly1rYma2k0yHNCclphs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Nov 2025 15:11:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+/1x5VNLgxPunpkoS/EDShG/ly1rYma2k0yHNCclphs="}]}, {"Route": "js/app.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\wg78tukmqu-1posqj9azf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2100"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IeIe2aS54ONtnLY1VQrOEwcC51f9qysdgbQPQ55G4Mc=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IeIe2aS54ONtnLY1VQrOEwcC51f9qysdgbQPQ55G4Mc="}]}, {"Route": "js/auth.iyy8yegl4k.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\8c7jydm8ty-iyy8yegl4k.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000880281690"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1135"}, {"Name": "ETag", "Value": "\"X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iyy8yegl4k"}, {"Name": "label", "Value": "js/auth.js"}, {"Name": "integrity", "Value": "sha256-BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0="}]}, {"Route": "js/auth.iyy8yegl4k.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\auth.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3765"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 15:25:05 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iyy8yegl4k"}, {"Name": "label", "Value": "js/auth.js"}, {"Name": "integrity", "Value": "sha256-BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0="}]}, {"Route": "js/auth.iyy8yegl4k.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\8c7jydm8ty-iyy8yegl4k.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1135"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iyy8yegl4k"}, {"Name": "label", "Value": "js/auth.js.gz"}, {"Name": "integrity", "Value": "sha256-X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc="}]}, {"Route": "js/auth.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\8c7jydm8ty-iyy8yegl4k.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000880281690"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1135"}, {"Name": "ETag", "Value": "\"X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0="}]}, {"Route": "js/auth.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\auth.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3765"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 15:25:05 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0="}]}, {"Route": "js/auth.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\8c7jydm8ty-iyy8yegl4k.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1135"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc="}]}, {"Route": "js/categories.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\vkpingxf2f-q4k5ude2ax.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000418235048"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2390"}, {"Name": "ETag", "Value": "\"N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4="}]}, {"Route": "js/categories.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\categories.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10343"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:53:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4="}]}, {"Route": "js/categories.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\vkpingxf2f-q4k5ude2ax.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2390"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM="}]}, {"Route": "js/categories.q4k5ude2ax.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\vkpingxf2f-q4k5ude2ax.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000418235048"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2390"}, {"Name": "ETag", "Value": "\"N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q4k5ude2ax"}, {"Name": "label", "Value": "js/categories.js"}, {"Name": "integrity", "Value": "sha256-Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4="}]}, {"Route": "js/categories.q4k5ude2ax.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\categories.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10343"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:53:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q4k5ude2ax"}, {"Name": "label", "Value": "js/categories.js"}, {"Name": "integrity", "Value": "sha256-Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4="}]}, {"Route": "js/categories.q4k5ude2ax.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\vkpingxf2f-q4k5ude2ax.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2390"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q4k5ude2ax"}, {"Name": "label", "Value": "js/categories.js.gz"}, {"Name": "integrity", "Value": "sha256-N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM="}]}, {"Route": "js/customers.hy3h9tabsi.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\ill7x5ozqf-hy3h9tabsi.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000372439479"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2684"}, {"Name": "ETag", "Value": "\"sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hy3h9tabsi"}, {"Name": "label", "Value": "js/customers.js"}, {"Name": "integrity", "Value": "sha256-tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc="}]}, {"Route": "js/customers.hy3h9tabsi.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\customers.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14539"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:52:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hy3h9tabsi"}, {"Name": "label", "Value": "js/customers.js"}, {"Name": "integrity", "Value": "sha256-tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc="}]}, {"Route": "js/customers.hy3h9tabsi.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\ill7x5ozqf-hy3h9tabsi.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2684"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hy3h9tabsi"}, {"Name": "label", "Value": "js/customers.js.gz"}, {"Name": "integrity", "Value": "sha256-sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4="}]}, {"Route": "js/customers.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\ill7x5ozqf-hy3h9tabsi.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000372439479"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2684"}, {"Name": "ETag", "Value": "\"sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc="}]}, {"Route": "js/customers.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\customers.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14539"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:52:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc="}]}, {"Route": "js/customers.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\ill7x5ozqf-hy3h9tabsi.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2684"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4="}]}, {"Route": "js/expenses.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\x62rvtqy4o-sw1pmnb1tp.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000290782204"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3438"}, {"Name": "ETag", "Value": "\"0x7sxXZp5/3Pq8eQxZvOOUu4iOcv28hKJy22PKDxe8A=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"FKgwCz13SOOZVbBKGsOlK1NphH4brqQY0N1fqBQ8QIs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FKgwCz13SOOZVbBKGsOlK1NphH4brqQY0N1fqBQ8QIs="}]}, {"Route": "js/expenses.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\expenses.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18166"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"FKgwCz13SOOZVbBKGsOlK1NphH4brqQY0N1fqBQ8QIs=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 18 Nov 2025 15:01:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FKgwCz13SOOZVbBKGsOlK1NphH4brqQY0N1fqBQ8QIs="}]}, {"Route": "js/expenses.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\x62rvtqy4o-sw1pmnb1tp.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3438"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0x7sxXZp5/3Pq8eQxZvOOUu4iOcv28hKJy22PKDxe8A=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0x7sxXZp5/3Pq8eQxZvOOUu4iOcv28hKJy22PKDxe8A="}]}, {"Route": "js/expenses.sw1pmnb1tp.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\x62rvtqy4o-sw1pmnb1tp.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000290782204"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3438"}, {"Name": "ETag", "Value": "\"0x7sxXZp5/3Pq8eQxZvOOUu4iOcv28hKJy22PKDxe8A=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"FKgwCz13SOOZVbBKGsOlK1NphH4brqQY0N1fqBQ8QIs=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sw1pmnb1tp"}, {"Name": "label", "Value": "js/expenses.js"}, {"Name": "integrity", "Value": "sha256-FKgwCz13SOOZVbBKGsOlK1NphH4brqQY0N1fqBQ8QIs="}]}, {"Route": "js/expenses.sw1pmnb1tp.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\expenses.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18166"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"FKgwCz13SOOZVbBKGsOlK1NphH4brqQY0N1fqBQ8QIs=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 18 Nov 2025 15:01:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sw1pmnb1tp"}, {"Name": "label", "Value": "js/expenses.js"}, {"Name": "integrity", "Value": "sha256-FKgwCz13SOOZVbBKGsOlK1NphH4brqQY0N1fqBQ8QIs="}]}, {"Route": "js/expenses.sw1pmnb1tp.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\x62rvtqy4o-sw1pmnb1tp.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3438"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0x7sxXZp5/3Pq8eQxZvOOUu4iOcv28hKJy22PKDxe8A=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sw1pmnb1tp"}, {"Name": "label", "Value": "js/expenses.js.gz"}, {"Name": "integrity", "Value": "sha256-0x7sxXZp5/3Pq8eQxZvOOUu4iOcv28hKJy22PKDxe8A="}]}, {"Route": "js/invoices.7ene3dnx54.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\sif3j9439v-7ene3dnx54.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000203417413"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4915"}, {"Name": "ETag", "Value": "\"oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7ene3dnx54"}, {"Name": "label", "Value": "js/invoices.js"}, {"Name": "integrity", "Value": "sha256-71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY="}]}, {"Route": "js/invoices.7ene3dnx54.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\invoices.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30213"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY=\""}, {"Name": "Last-Modified", "Value": "Wed, 12 Nov 2025 19:27:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7ene3dnx54"}, {"Name": "label", "Value": "js/invoices.js"}, {"Name": "integrity", "Value": "sha256-71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY="}]}, {"Route": "js/invoices.7ene3dnx54.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\sif3j9439v-7ene3dnx54.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4915"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7ene3dnx54"}, {"Name": "label", "Value": "js/invoices.js.gz"}, {"Name": "integrity", "Value": "sha256-oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI="}]}, {"Route": "js/invoices.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\sif3j9439v-7ene3dnx54.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000203417413"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4915"}, {"Name": "ETag", "Value": "\"oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY="}]}, {"Route": "js/invoices.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\invoices.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30213"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY=\""}, {"Name": "Last-Modified", "Value": "Wed, 12 Nov 2025 19:27:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY="}]}, {"Route": "js/invoices.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\sif3j9439v-7ene3dnx54.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4915"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI="}]}, {"Route": "js/pos.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\5lixpq0z40-pjnfkijmjf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000129550460"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7718"}, {"Name": "ETag", "Value": "\"Y9AAQFdpxbnxeOLCYmeDn/xDQvyT15WTuIgo2q+uaA8=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"9fBx2HYGq8pnWX4y638wJhSf2lSjcp8kaLo0vbBXo6c=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9fBx2HYGq8pnWX4y638wJhSf2lSjcp8kaLo0vbBXo6c="}]}, {"Route": "js/pos.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\pos.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "37680"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9fBx2HYGq8pnWX4y638wJhSf2lSjcp8kaLo0vbBXo6c=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:06:29 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9fBx2HYGq8pnWX4y638wJhSf2lSjcp8kaLo0vbBXo6c="}]}, {"Route": "js/pos.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\5lixpq0z40-pjnfkijmjf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7718"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Y9AAQFdpxbnxeOLCYmeDn/xDQvyT15WTuIgo2q+uaA8=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Y9AAQFdpxbnxeOLCYmeDn/xDQvyT15WTuIgo2q+uaA8="}]}, {"Route": "js/pos.pjnfkijmjf.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\5lixpq0z40-pjnfkijmjf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000129550460"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7718"}, {"Name": "ETag", "Value": "\"Y9AAQFdpxbnxeOLCYmeDn/xDQvyT15WTuIgo2q+uaA8=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"9fBx2HYGq8pnWX4y638wJhSf2lSjcp8kaLo0vbBXo6c=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pjnfkijmjf"}, {"Name": "label", "Value": "js/pos.js"}, {"Name": "integrity", "Value": "sha256-9fBx2HYGq8pnWX4y638wJhSf2lSjcp8kaLo0vbBXo6c="}]}, {"Route": "js/pos.pjnfkijmjf.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\pos.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "37680"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9fBx2HYGq8pnWX4y638wJhSf2lSjcp8kaLo0vbBXo6c=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:06:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pjnfkijmjf"}, {"Name": "label", "Value": "js/pos.js"}, {"Name": "integrity", "Value": "sha256-9fBx2HYGq8pnWX4y638wJhSf2lSjcp8kaLo0vbBXo6c="}]}, {"Route": "js/pos.pjnfkijmjf.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\5lixpq0z40-pjnfkijmjf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7718"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Y9AAQFdpxbnxeOLCYmeDn/xDQvyT15WTuIgo2q+uaA8=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pjnfkijmjf"}, {"Name": "label", "Value": "js/pos.js.gz"}, {"Name": "integrity", "Value": "sha256-Y9AAQFdpxbnxeOLCYmeDn/xDQvyT15WTuIgo2q+uaA8="}]}, {"Route": "js/products.9n0jr0b026.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\wa9tzd9v0l-9n0jr0b026.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000120700060"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8284"}, {"Name": "ETag", "Value": "\"cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9n0jr0b026"}, {"Name": "label", "Value": "js/products.js"}, {"Name": "integrity", "Value": "sha256-+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0="}]}, {"Route": "js/products.9n0jr0b026.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\products.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44477"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:35:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9n0jr0b026"}, {"Name": "label", "Value": "js/products.js"}, {"Name": "integrity", "Value": "sha256-+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0="}]}, {"Route": "js/products.9n0jr0b026.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\wa9tzd9v0l-9n0jr0b026.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8284"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9n0jr0b026"}, {"Name": "label", "Value": "js/products.js.gz"}, {"Name": "integrity", "Value": "sha256-cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU="}]}, {"Route": "js/products.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\wa9tzd9v0l-9n0jr0b026.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000120700060"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8284"}, {"Name": "ETag", "Value": "\"cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0="}]}, {"Route": "js/products.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\products.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44477"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:35:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0="}]}, {"Route": "js/products.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\wa9tzd9v0l-9n0jr0b026.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8284"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU="}]}, {"Route": "js/purchases.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\j099mgtiqd-uxkkg3wsx6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000259403372"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3854"}, {"Name": "ETag", "Value": "\"L2DAn2hNrvcZKW4yRq71tVHjOcqPKCI67MD5j5IHvmI=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"/lz99RppGma8f0Q9M/FfxUZAZyuStL95Lx7uSeAikc8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/lz99RppGma8f0Q9M/FfxUZAZyuStL95Lx7uSeAikc8="}]}, {"Route": "js/purchases.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\purchases.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19719"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/lz99RppGma8f0Q9M/FfxUZAZyuStL95Lx7uSeAikc8=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 18 Nov 2025 15:42:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/lz99RppGma8f0Q9M/FfxUZAZyuStL95Lx7uSeAikc8="}]}, {"Route": "js/purchases.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\j099mgtiqd-uxkkg3wsx6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3854"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"L2DAn2hNrvcZKW4yRq71tVHjOcqPKCI67MD5j5IHvmI=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L2DAn2hNrvcZKW4yRq71tVHjOcqPKCI67MD5j5IHvmI="}]}, {"Route": "js/purchases.uxkkg3wsx6.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\j099mgtiqd-uxkkg3wsx6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000259403372"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3854"}, {"Name": "ETag", "Value": "\"L2DAn2hNrvcZKW4yRq71tVHjOcqPKCI67MD5j5IHvmI=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"/lz99RppGma8f0Q9M/FfxUZAZyuStL95Lx7uSeAikc8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uxkkg3wsx6"}, {"Name": "label", "Value": "js/purchases.js"}, {"Name": "integrity", "Value": "sha256-/lz99RppGma8f0Q9M/FfxUZAZyuStL95Lx7uSeAikc8="}]}, {"Route": "js/purchases.uxkkg3wsx6.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\purchases.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19719"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/lz99RppGma8f0Q9M/FfxUZAZyuStL95Lx7uSeAikc8=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 18 Nov 2025 15:42:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uxkkg3wsx6"}, {"Name": "label", "Value": "js/purchases.js"}, {"Name": "integrity", "Value": "sha256-/lz99RppGma8f0Q9M/FfxUZAZyuStL95Lx7uSeAikc8="}]}, {"Route": "js/purchases.uxkkg3wsx6.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\j099mgtiqd-uxkkg3wsx6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3854"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"L2DAn2hNrvcZKW4yRq71tVHjOcqPKCI67MD5j5IHvmI=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uxkkg3wsx6"}, {"Name": "label", "Value": "js/purchases.js.gz"}, {"Name": "integrity", "Value": "sha256-L2DAn2hNrvcZKW4yRq71tVHjOcqPKCI67MD5j5IHvmI="}]}, {"Route": "js/reports.2zj8ofrn42.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\x78a6k87cc-2zj8ofrn42.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000147601476"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6774"}, {"Name": "ETag", "Value": "\"oMYbV5AVYI0xejYZoQHZnI3REekGeYg+bDNc3rpK6pk=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"4sSIKiYZB5nuL7KWTQ1ImL4vKWYy7+KL+TfYKCHThQQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2zj8ofrn42"}, {"Name": "label", "Value": "js/reports.js"}, {"Name": "integrity", "Value": "sha256-4sSIKiYZB5nuL7KWTQ1ImL4vKWYy7+KL+TfYKCHThQQ="}]}, {"Route": "js/reports.2zj8ofrn42.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\reports.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "57134"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4sSIKiYZB5nuL7KWTQ1ImL4vKWYy7+KL+TfYKCHThQQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Nov 2025 14:05:57 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2zj8ofrn42"}, {"Name": "label", "Value": "js/reports.js"}, {"Name": "integrity", "Value": "sha256-4sSIKiYZB5nuL7KWTQ1ImL4vKWYy7+KL+TfYKCHThQQ="}]}, {"Route": "js/reports.2zj8ofrn42.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\x78a6k87cc-2zj8ofrn42.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6774"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oMYbV5AVYI0xejYZoQHZnI3REekGeYg+bDNc3rpK6pk=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2zj8ofrn42"}, {"Name": "label", "Value": "js/reports.js.gz"}, {"Name": "integrity", "Value": "sha256-oMYbV5AVYI0xejYZoQHZnI3REekGeYg+bDNc3rpK6pk="}]}, {"Route": "js/reports.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\x78a6k87cc-2zj8ofrn42.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000147601476"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6774"}, {"Name": "ETag", "Value": "\"oMYbV5AVYI0xejYZoQHZnI3REekGeYg+bDNc3rpK6pk=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"4sSIKiYZB5nuL7KWTQ1ImL4vKWYy7+KL+TfYKCHThQQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4sSIKiYZB5nuL7KWTQ1ImL4vKWYy7+KL+TfYKCHThQQ="}]}, {"Route": "js/reports.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\reports.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "57134"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4sSIKiYZB5nuL7KWTQ1ImL4vKWYy7+KL+TfYKCHThQQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Nov 2025 14:05:57 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4sSIKiYZB5nuL7KWTQ1ImL4vKWYy7+KL+TfYKCHThQQ="}]}, {"Route": "js/reports.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\x78a6k87cc-2zj8ofrn42.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6774"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oMYbV5AVYI0xejYZoQHZnI3REekGeYg+bDNc3rpK6pk=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oMYbV5AVYI0xejYZoQHZnI3REekGeYg+bDNc3rpK6pk="}]}, {"Route": "js/settings.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\kd4at48or8-kgsze9z630.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000076068766"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13145"}, {"Name": "ETag", "Value": "\"uc88cmSCVyx4BQE3QaHAfuGDykSaV72SH+Z0KBHEbvg=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"LEPouUi0qenCXl3Oxe6EXY+crHhr0tJljs8+eaVn9KU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LEPouUi0qenCXl3Oxe6EXY+crHhr0tJljs8+eaVn9KU="}]}, {"Route": "js/settings.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\settings.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "90954"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LEPouUi0qenCXl3Oxe6EXY+crHhr0tJljs8+eaVn9KU=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 17:40:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LEPouUi0qenCXl3Oxe6EXY+crHhr0tJljs8+eaVn9KU="}]}, {"Route": "js/settings.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\kd4at48or8-kgsze9z630.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13145"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uc88cmSCVyx4BQE3QaHAfuGDykSaV72SH+Z0KBHEbvg=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uc88cmSCVyx4BQE3QaHAfuGDykSaV72SH+Z0KBHEbvg="}]}, {"Route": "js/settings.kgsze9z630.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\kd4at48or8-kgsze9z630.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000076068766"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13145"}, {"Name": "ETag", "Value": "\"uc88cmSCVyx4BQE3QaHAfuGDykSaV72SH+Z0KBHEbvg=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"LEPouUi0qenCXl3Oxe6EXY+crHhr0tJljs8+eaVn9KU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kgsze9z630"}, {"Name": "label", "Value": "js/settings.js"}, {"Name": "integrity", "Value": "sha256-LEPouUi0qenCXl3Oxe6EXY+crHhr0tJljs8+eaVn9KU="}]}, {"Route": "js/settings.kgsze9z630.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\settings.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "90954"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LEPouUi0qenCXl3Oxe6EXY+crHhr0tJljs8+eaVn9KU=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 17:40:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kgsze9z630"}, {"Name": "label", "Value": "js/settings.js"}, {"Name": "integrity", "Value": "sha256-LEPouUi0qenCXl3Oxe6EXY+crHhr0tJljs8+eaVn9KU="}]}, {"Route": "js/settings.kgsze9z630.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\kd4at48or8-kgsze9z630.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13145"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uc88cmSCVyx4BQE3QaHAfuGDykSaV72SH+Z0KBHEbvg=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kgsze9z630"}, {"Name": "label", "Value": "js/settings.js.gz"}, {"Name": "integrity", "Value": "sha256-uc88cmSCVyx4BQE3QaHAfuGDykSaV72SH+Z0KBHEbvg="}]}, {"Route": "js/suppliers.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\eoulc2yy7k-sb2z2q5wz2.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000415627598"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2405"}, {"Name": "ETag", "Value": "\"G4zjpvKHBa9FT9vBtheUVUNyGnh082JCUyN20sqgGgE=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"eM9g8qJqseSo9j4vYUbEBrdPIVvXd/JVkK87clenhso=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eM9g8qJqseSo9j4vYUbEBrdPIVvXd/JVkK87clenhso="}]}, {"Route": "js/suppliers.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\suppliers.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10417"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eM9g8qJqseSo9j4vYUbEBrdPIVvXd/JVkK87clenhso=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 18 Nov 2025 15:30:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eM9g8qJqseSo9j4vYUbEBrdPIVvXd/JVkK87clenhso="}]}, {"Route": "js/suppliers.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\eoulc2yy7k-sb2z2q5wz2.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2405"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"G4zjpvKHBa9FT9vBtheUVUNyGnh082JCUyN20sqgGgE=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G4zjpvKHBa9FT9vBtheUVUNyGnh082JCUyN20sqgGgE="}]}, {"Route": "js/suppliers.sb2z2q5wz2.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\eoulc2yy7k-sb2z2q5wz2.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000415627598"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2405"}, {"Name": "ETag", "Value": "\"G4zjpvKHBa9FT9vBtheUVUNyGnh082JCUyN20sqgGgE=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"eM9g8qJqseSo9j4vYUbEBrdPIVvXd/JVkK87clenhso=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sb2z2q5wz2"}, {"Name": "label", "Value": "js/suppliers.js"}, {"Name": "integrity", "Value": "sha256-eM9g8qJqseSo9j4vYUbEBrdPIVvXd/JVkK87clenhso="}]}, {"Route": "js/suppliers.sb2z2q5wz2.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\suppliers.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10417"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eM9g8qJqseSo9j4vYUbEBrdPIVvXd/JVkK87clenhso=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 18 Nov 2025 15:30:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sb2z2q5wz2"}, {"Name": "label", "Value": "js/suppliers.js"}, {"Name": "integrity", "Value": "sha256-eM9g8qJqseSo9j4vYUbEBrdPIVvXd/JVkK87clenhso="}]}, {"Route": "js/suppliers.sb2z2q5wz2.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\eoulc2yy7k-sb2z2q5wz2.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2405"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"G4zjpvKHBa9FT9vBtheUVUNyGnh082JCUyN20sqgGgE=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sb2z2q5wz2"}, {"Name": "label", "Value": "js/suppliers.js.gz"}, {"Name": "integrity", "Value": "sha256-G4zjpvKHBa9FT9vBtheUVUNyGnh082JCUyN20sqgGgE="}]}, {"Route": "login.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\ziqm8tcidl-nqonojhrly.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000487329435"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2051"}, {"Name": "ETag", "Value": "\"701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ="}]}, {"Route": "login.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\login.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6732"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 15:38:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ="}]}, {"Route": "login.html.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\ziqm8tcidl-nqonojhrly.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2051"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ="}]}, {"Route": "login.nqonojhrly.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\ziqm8tcidl-nqonojhrly.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000487329435"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2051"}, {"Name": "ETag", "Value": "\"701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "login.html"}, {"Name": "integrity", "Value": "sha256-//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ="}]}, {"Route": "login.nqonojhrly.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\login.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6732"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 15:38:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "login.html"}, {"Name": "integrity", "Value": "sha256-//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ="}]}, {"Route": "login.nqonojhrly.html.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\ziqm8tcidl-nqonojhrly.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2051"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "login.html.gz"}, {"Name": "integrity", "Value": "sha256-701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ="}]}, {"Route": "print-invoice.2cv1skr50a.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\pv27i78e2o-2cv1skr50a.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000250752257"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3987"}, {"Name": "ETag", "Value": "\"bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2cv1skr50a"}, {"Name": "label", "Value": "print-invoice.html"}, {"Name": "integrity", "Value": "sha256-4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0="}]}, {"Route": "print-invoice.2cv1skr50a.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\print-invoice.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17980"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 19:09:19 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2cv1skr50a"}, {"Name": "label", "Value": "print-invoice.html"}, {"Name": "integrity", "Value": "sha256-4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0="}]}, {"Route": "print-invoice.2cv1skr50a.html.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\pv27i78e2o-2cv1skr50a.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3987"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2cv1skr50a"}, {"Name": "label", "Value": "print-invoice.html.gz"}, {"Name": "integrity", "Value": "sha256-bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE="}]}, {"Route": "print-invoice.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\pv27i78e2o-2cv1skr50a.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000250752257"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3987"}, {"Name": "ETag", "Value": "\"bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0="}]}, {"Route": "print-invoice.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\print-invoice.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17980"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 19:09:19 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0="}]}, {"Route": "print-invoice.html.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\pv27i78e2o-2cv1skr50a.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3987"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Nov 2025 14:43:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE="}]}]}