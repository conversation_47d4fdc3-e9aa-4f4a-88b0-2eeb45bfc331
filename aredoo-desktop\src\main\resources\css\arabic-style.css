/* Arabic RTL Support and General Styling */

/* Root styling */
.root {
    -fx-font-family: "Arial", "Tahoma", "Segoe UI";
    -fx-font-size: 12px;
    -fx-base: #f4f4f4;
    -fx-background: #ffffff;
}

/* Login Container */
.login-container {
    -fx-background-color: linear-gradient(to bottom, #667eea 0%, #764ba2 100%);
    -fx-padding: 30;
}

.app-title {
    -fx-text-fill: white;
    -fx-font-size: 28px;
    -fx-font-weight: bold;
}

.app-subtitle {
    -fx-text-fill: #e0e0e0;
    -fx-font-size: 14px;
}

.login-form {
    -fx-background-color: rgba(255, 255, 255, 0.95);
    -fx-background-radius: 10;
    -fx-padding: 30;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.3), 10, 0, 0, 5);
}

.field-label {
    -fx-text-fill: #333333;
    -fx-font-weight: bold;
}

.text-field-arabic {
    -fx-font-size: 12px;
    -fx-padding: 8;
    -fx-background-radius: 5;
    -fx-border-radius: 5;
    -fx-border-color: #cccccc;
    -fx-text-alignment: right;
}

.text-field-arabic:focused {
    -fx-border-color: #667eea;
    -fx-border-width: 2;
}

.login-button {
    -fx-background-color: #667eea;
    -fx-text-fill: white;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-background-radius: 5;
    -fx-cursor: hand;
}

.login-button:hover {
    -fx-background-color: #5a6fd8;
}

.login-button:pressed {
    -fx-background-color: #4e63c7;
}

.error-label {
    -fx-text-fill: #d32f2f;
    -fx-font-size: 11px;
}

.info-label {
    -fx-text-fill: #666666;
    -fx-font-size: 10px;
}

/* Main Window Styling */
.main-container {
    -fx-background-color: #f5f5f5;
}

.menu-bar {
    -fx-background-color: #2c3e50;
}

.menu-bar .menu {
    -fx-text-fill: white;
}

.menu-bar .menu:hover {
    -fx-background-color: #34495e;
}

.toolbar {
    -fx-background-color: #ecf0f1;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 0 0 1 0;
    -fx-padding: 5;
}

.toolbar-button {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
    -fx-padding: 8 15;
    -fx-background-radius: 3;
    -fx-cursor: hand;
}

.toolbar-button:hover {
    -fx-background-color: #2980b9;
}

.user-label {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
}

.main-tab-pane {
    -fx-background-color: white;
}

.main-tab-pane .tab {
    -fx-background-color: #ecf0f1;
    -fx-text-alignment: center;
}

.main-tab-pane .tab:selected {
    -fx-background-color: white;
}

/* Dashboard Styling */
.dashboard-container {
    -fx-background-color: white;
}

.welcome-section {
    -fx-alignment: center;
    -fx-padding: 20;
    -fx-background-color: linear-gradient(to right, #667eea, #764ba2);
    -fx-background-radius: 10;
}

.welcome-title {
    -fx-text-fill: white;
    -fx-font-size: 24px;
    -fx-font-weight: bold;
}

.welcome-subtitle {
    -fx-text-fill: #e0e0e0;
    -fx-font-size: 14px;
}

.stats-grid {
    -fx-alignment: center;
}

.stat-card {
    -fx-background-color: white;
    -fx-background-radius: 8;
    -fx-padding: 20;
    -fx-alignment: center;
    -fx-spacing: 10;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);
    -fx-min-width: 150;
    -fx-min-height: 100;
}

.stat-card:hover {
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 8, 0, 0, 3);
}

.alert-card {
    -fx-background-color: #fff3cd;
    -fx-border-color: #ffeaa7;
    -fx-border-width: 1;
    -fx-border-radius: 8;
}

.stat-title {
    -fx-text-fill: #666666;
    -fx-font-size: 12px;
}

.stat-value {
    -fx-text-fill: #2c3e50;
    -fx-font-size: 24px;
    -fx-font-weight: bold;
}

.stat-subtitle {
    -fx-text-fill: #95a5a6;
    -fx-font-size: 10px;
}

.quick-actions {
    -fx-background-color: white;
    -fx-background-radius: 8;
    -fx-padding: 20;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);
}

.section-title {
    -fx-text-fill: #2c3e50;
    -fx-font-size: 16px;
    -fx-font-weight: bold;
}

.quick-action-button {
    -fx-background-color: #27ae60;
    -fx-text-fill: white;
    -fx-padding: 12 20;
    -fx-background-radius: 5;
    -fx-cursor: hand;
    -fx-font-size: 12px;
}

.quick-action-button:hover {
    -fx-background-color: #229954;
}

/* Status Bar */
.status-bar {
    -fx-background-color: #ecf0f1;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1 0 0 0;
}

.status-label {
    -fx-text-fill: #2c3e50;
    -fx-font-size: 11px;
}

.datetime-label {
    -fx-text-fill: #7f8c8d;
    -fx-font-size: 11px;
}

/* Table Styling */
.table-view {
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
}

.table-view .column-header {
    -fx-background-color: #34495e;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-alignment: center;
}

.table-view .table-cell {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

/* Button Styling */
.button {
    -fx-background-radius: 3;
    -fx-cursor: hand;
    -fx-font-size: 12px;
}

.button:hover {
    -fx-opacity: 0.8;
}

/* Form Styling */
.form-container {
    -fx-background-color: white;
    -fx-padding: 20;
    -fx-spacing: 15;
}

.form-field {
    -fx-spacing: 5;
}

.form-field .label {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
}

.form-field .text-field,
.form-field .text-area,
.form-field .combo-box {
    -fx-text-alignment: right;
    -fx-alignment: center-right;
}

/* Arabic RTL Support */
.text-field, .text-area, .combo-box {
    -fx-text-alignment: right;
    -fx-alignment: center-right;
}

.table-view .table-cell {
    -fx-text-alignment: right;
    -fx-alignment: center-right;
}

.combo-box .list-cell {
    -fx-text-alignment: right;
    -fx-alignment: center-right;
}

/* Enhanced Arabic Typography */
.arabic-text {
    -fx-font-family: "Segoe UI", "Tahoma", "Arial Unicode MS", "Lucida Sans Unicode";
    -fx-text-alignment: right;
}

/* Date Picker Arabic Support */
.date-picker .text-field {
    -fx-text-alignment: right;
}

.date-picker-popup {
    -fx-text-alignment: right;
}

/* ComboBox Arabic Support */
.combo-box-popup .list-view {
    -fx-text-alignment: right;
}

.combo-box-popup .list-view .list-cell {
    -fx-text-alignment: right;
    -fx-alignment: center-right;
}

/* Menu Arabic Support */
.context-menu {
    -fx-text-alignment: right;
}

.context-menu .menu-item {
    -fx-text-alignment: right;
}

/* Dialog Arabic Support */
.dialog-pane {
    -fx-text-alignment: right;
}

.dialog-pane .header-panel {
    -fx-text-alignment: right;
}

.dialog-pane .content {
    -fx-text-alignment: right;
}

/* Alert Dialog Arabic Support */
.alert .header-panel .label {
    -fx-text-alignment: right;
}

.alert .content .label {
    -fx-text-alignment: right;
}

/* Tab Pane Arabic Support */
.tab-pane .tab {
    -fx-text-alignment: center;
}

.tab-pane .tab .tab-label {
    -fx-text-alignment: center;
}

/* Tooltip Arabic Support */
.tooltip {
    -fx-text-alignment: right;
}

/* Progress Indicator Arabic Support */
.progress-indicator .text {
    -fx-text-alignment: right;
}

/* Spinner Arabic Support */
.spinner .text-field {
    -fx-text-alignment: right;
}
