package com.aredoo.desktop.service;

import com.aredoo.desktop.model.User;
import org.springframework.stereotype.Service;

@Service
public class UserSession {
    
    private User currentUser;
    
    public User getCurrentUser() {
        return currentUser;
    }
    
    public void setCurrentUser(User user) {
        this.currentUser = user;
    }
    
    public boolean isLoggedIn() {
        return currentUser != null;
    }
    
    public boolean isAdmin() {
        return currentUser != null && "Admin".equals(currentUser.getRole());
    }
    
    public boolean isCashier() {
        return currentUser != null && "Cashier".equals(currentUser.getRole());
    }
    
    public String getCurrentUserName() {
        return currentUser != null ? currentUser.getFullName() : "";
    }
    
    public String getCurrentUserRole() {
        return currentUser != null ? currentUser.getRole() : "";
    }
    
    public void logout() {
        this.currentUser = null;
    }
}
