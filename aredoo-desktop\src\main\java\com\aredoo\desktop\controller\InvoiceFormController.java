package com.aredoo.desktop.controller;

import com.aredoo.desktop.model.*;
import com.aredoo.desktop.repository.*;
import com.aredoo.desktop.service.UserSession;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.stage.Stage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

@Controller
public class InvoiceFormController {

    @FXML private Label invoiceNumberLabel;
    @FXML private Label invoiceDateLabel;
    @FXML private ComboBox<Customer> customerComboBox;
    @FXML private ComboBox<String> paymentMethodComboBox;
    @FXML private TextField productSearchField;
    @FXML private TextField quantityField;
    @FXML private TableView<InvoiceItem> invoiceItemsTable;
    @FXML private TableColumn<InvoiceItem, String> itemCodeColumn;
    @FXML private TableColumn<InvoiceItem, String> itemNameColumn;
    @FXML private TableColumn<InvoiceItem, String> itemUnitColumn;
    @FXML private TableColumn<InvoiceItem, Integer> itemQuantityColumn;
    @FXML private TableColumn<InvoiceItem, BigDecimal> itemPriceColumn;
    @FXML private TableColumn<InvoiceItem, BigDecimal> itemTotalColumn;
    @FXML private TableColumn<InvoiceItem, Void> itemActionsColumn;
    @FXML private TextArea notesArea;
    @FXML private Label subtotalLabel;
    @FXML private TextField discountField;
    @FXML private TextField taxField;
    @FXML private Label totalLabel;
    @FXML private Label statusLabel;

    @Autowired private InvoiceRepository invoiceRepository;
    @Autowired private CustomerRepository customerRepository;
    @Autowired private ProductRepository productRepository;
    @Autowired private UserSession userSession;

    private ObservableList<InvoiceItem> invoiceItems = FXCollections.observableArrayList();
    private Invoice currentInvoice;

    @FXML
    private void initialize() {
        setupInvoiceInfo();
        setupTableColumns();
        loadCustomers();
        setupCalculations();
        
        // Set default payment method
        paymentMethodComboBox.setValue("نقداً");
        
        // Initialize quantity field
        quantityField.setText("1");
    }

    private void setupInvoiceInfo() {
        // Generate invoice number
        String invoiceNumber = generateInvoiceNumber();
        invoiceNumberLabel.setText(invoiceNumber);
        
        // Set current date
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        invoiceDateLabel.setText(now.format(formatter));
    }

    private void setupTableColumns() {
        itemCodeColumn.setCellValueFactory(data -> 
            data.getValue().getProduct().codeProperty());
        itemNameColumn.setCellValueFactory(data -> 
            data.getValue().getProduct().nameArProperty());
        itemUnitColumn.setCellValueFactory(data -> 
            data.getValue().getProduct().unitProperty());
        itemQuantityColumn.setCellValueFactory(new PropertyValueFactory<>("quantity"));
        itemPriceColumn.setCellValueFactory(new PropertyValueFactory<>("unitPrice"));
        itemTotalColumn.setCellValueFactory(new PropertyValueFactory<>("totalPrice"));

        // Format price columns
        itemPriceColumn.setCellFactory(column -> new TableCell<InvoiceItem, BigDecimal>() {
            @Override
            protected void updateItem(BigDecimal price, boolean empty) {
                super.updateItem(price, empty);
                if (empty || price == null) {
                    setText(null);
                } else {
                    setText(String.format("%.2f", price.doubleValue()));
                }
            }
        });

        itemTotalColumn.setCellFactory(column -> new TableCell<InvoiceItem, BigDecimal>() {
            @Override
            protected void updateItem(BigDecimal total, boolean empty) {
                super.updateItem(total, empty);
                if (empty || total == null) {
                    setText(null);
                } else {
                    setText(String.format("%.2f", total.doubleValue()));
                }
            }
        });

        // Setup actions column
        itemActionsColumn.setCellFactory(column -> new TableCell<InvoiceItem, Void>() {
            private final Button removeButton = new Button("حذف");

            {
                removeButton.setOnAction(e -> {
                    InvoiceItem item = getTableView().getItems().get(getIndex());
                    removeItemFromInvoice(item);
                });
                removeButton.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-size: 10px;");
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(removeButton);
                }
            }
        });

        invoiceItemsTable.setItems(invoiceItems);
    }

    private void loadCustomers() {
        try {
            List<Customer> customers = customerRepository.findByIsActiveTrueOrderByName();
            customerComboBox.getItems().addAll(customers);
            
            // Set converter to display customer name
            customerComboBox.setConverter(new javafx.util.StringConverter<Customer>() {
                @Override
                public String toString(Customer customer) {
                    return customer != null ? customer.getName() : "";
                }

                @Override
                public Customer fromString(String string) {
                    return customerComboBox.getItems().stream()
                        .filter(customer -> customer.getName().equals(string))
                        .findFirst().orElse(null);
                }
            });
            
        } catch (Exception e) {
            showError("خطأ في تحميل العملاء: " + e.getMessage());
        }
    }

    private void setupCalculations() {
        // Listen for changes in discount and tax fields
        discountField.textProperty().addListener((obs, oldVal, newVal) -> calculateTotal());
        taxField.textProperty().addListener((obs, oldVal, newVal) -> calculateTotal());
        
        // Validate numeric input
        discountField.textProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal.matches("\\d*\\.?\\d*")) {
                discountField.setText(oldVal);
            }
        });
        
        taxField.textProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal.matches("\\d*\\.?\\d*")) {
                taxField.setText(oldVal);
            }
        });
    }

    @FXML
    private void searchProduct() {
        String searchTerm = productSearchField.getText().trim();
        if (searchTerm.isEmpty()) {
            showWarning("يرجى إدخال اسم أو كود المنتج");
            return;
        }

        try {
            List<Product> products = productRepository.searchProducts(searchTerm);
            if (products.isEmpty()) {
                showWarning("لم يتم العثور على منتجات");
                return;
            }

            // If only one product found, select it automatically
            if (products.size() == 1) {
                Product product = products.get(0);
                addProductToInvoice(product);
            } else {
                // Show product selection dialog
                showProductSelectionDialog(products);
            }

        } catch (Exception e) {
            showError("خطأ في البحث: " + e.getMessage());
        }
    }

    @FXML
    private void addProductToInvoice() {
        searchProduct();
    }

    private void addProductToInvoice(Product product) {
        try {
            int quantity = Integer.parseInt(quantityField.getText().trim());
            if (quantity <= 0) {
                showWarning("الكمية يجب أن تكون أكبر من صفر");
                return;
            }

            // Check if product already exists in invoice
            Optional<InvoiceItem> existingItem = invoiceItems.stream()
                .filter(item -> item.getProduct().getId().equals(product.getId()))
                .findFirst();

            if (existingItem.isPresent()) {
                // Update quantity
                InvoiceItem item = existingItem.get();
                item.setQuantity(item.getQuantity() + quantity);
                item.setTotalPrice(item.getUnitPrice().multiply(BigDecimal.valueOf(item.getQuantity())));
            } else {
                // Add new item
                InvoiceItem newItem = new InvoiceItem();
                newItem.setProduct(product);
                newItem.setQuantity(quantity);
                newItem.setUnitPrice(product.getSalePrice());
                newItem.setTotalPrice(product.getSalePrice().multiply(BigDecimal.valueOf(quantity)));
                
                invoiceItems.add(newItem);
            }

            // Clear search field and reset quantity
            productSearchField.clear();
            quantityField.setText("1");
            
            // Recalculate total
            calculateTotal();
            
            updateStatus("تم إضافة المنتج بنجاح");

        } catch (NumberFormatException e) {
            showWarning("يرجى إدخال كمية صحيحة");
        } catch (Exception e) {
            showError("خطأ في إضافة المنتج: " + e.getMessage());
        }
    }

    private void removeItemFromInvoice(InvoiceItem item) {
        invoiceItems.remove(item);
        calculateTotal();
        updateStatus("تم حذف المنتج من الفاتورة");
    }

    private void calculateTotal() {
        BigDecimal subtotal = invoiceItems.stream()
            .map(InvoiceItem::getTotalPrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        subtotalLabel.setText(String.format("%.2f د.ع", subtotal.doubleValue()));

        // Calculate discount
        BigDecimal discount = BigDecimal.ZERO;
        try {
            String discountText = discountField.getText().trim();
            if (!discountText.isEmpty()) {
                discount = new BigDecimal(discountText);
            }
        } catch (NumberFormatException e) {
            discount = BigDecimal.ZERO;
        }

        // Calculate tax
        BigDecimal taxRate = BigDecimal.ZERO;
        try {
            String taxText = taxField.getText().trim();
            if (!taxText.isEmpty()) {
                taxRate = new BigDecimal(taxText);
            }
        } catch (NumberFormatException e) {
            taxRate = BigDecimal.ZERO;
        }

        BigDecimal afterDiscount = subtotal.subtract(discount);
        BigDecimal tax = afterDiscount.multiply(taxRate).divide(BigDecimal.valueOf(100));
        BigDecimal total = afterDiscount.add(tax);

        totalLabel.setText(String.format("%.2f د.ع", total.doubleValue()));
    }

    @FXML
    private void addNewCustomer() {
        // TODO: Open customer form dialog
        showInfo("سيتم تنفيذ هذه الميزة قريباً");
    }

    @FXML
    private void saveInvoice() {
        if (!validateInvoice()) {
            return;
        }

        try {
            // Create new invoice
            Invoice invoice = new Invoice();
            invoice.setInvoiceNumber(invoiceNumberLabel.getText());
            invoice.setInvoiceDate(LocalDateTime.now());
            invoice.setCustomer(customerComboBox.getValue());
            invoice.setPaymentMethod(paymentMethodComboBox.getValue());
            invoice.setNotes(notesArea.getText());
            invoice.setCreatedBy(userSession.getCurrentUser());
            invoice.setCreatedAt(LocalDateTime.now());

            // Calculate totals
            BigDecimal subtotal = invoiceItems.stream()
                .map(InvoiceItem::getTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal discount = new BigDecimal(discountField.getText().isEmpty() ? "0" : discountField.getText());
            BigDecimal taxRate = new BigDecimal(taxField.getText().isEmpty() ? "0" : taxField.getText());
            BigDecimal tax = subtotal.subtract(discount).multiply(taxRate).divide(BigDecimal.valueOf(100));
            BigDecimal total = subtotal.subtract(discount).add(tax);

            invoice.setSubtotal(subtotal);
            invoice.setDiscountAmount(discount);
            invoice.setTaxAmount(tax);
            invoice.setTotalAmount(total);
            invoice.setPaidAmount(total); // Assume fully paid for now
            invoice.setStatus("مدفوعة");

            // Save invoice
            Invoice savedInvoice = invoiceRepository.save(invoice);

            // Save invoice items
            for (InvoiceItem item : invoiceItems) {
                item.setInvoice(savedInvoice);
                // TODO: Save invoice items
            }

            showInfo("تم حفظ الفاتورة بنجاح");
            closeWindow();

        } catch (Exception e) {
            showError("خطأ في حفظ الفاتورة: " + e.getMessage());
        }
    }

    @FXML
    private void printInvoice() {
        // TODO: Implement printing
        showInfo("سيتم تنفيذ الطباعة قريباً");
    }

    @FXML
    private void cancel() {
        closeWindow();
    }

    private boolean validateInvoice() {
        if (customerComboBox.getValue() == null) {
            showWarning("يرجى اختيار العميل");
            return false;
        }

        if (invoiceItems.isEmpty()) {
            showWarning("يرجى إضافة منتجات للفاتورة");
            return false;
        }

        return true;
    }

    private String generateInvoiceNumber() {
        // Simple invoice number generation
        return "INV-" + System.currentTimeMillis();
    }

    private void showProductSelectionDialog(List<Product> products) {
        // TODO: Implement product selection dialog
        if (!products.isEmpty()) {
            addProductToInvoice(products.get(0));
        }
    }

    private void updateStatus(String message) {
        statusLabel.setText(message);
    }

    private void showError(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("خطأ");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showWarning(String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("تحذير");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showInfo(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("معلومات");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void closeWindow() {
        Stage stage = (Stage) invoiceNumberLabel.getScene().getWindow();
        stage.close();
    }
}
