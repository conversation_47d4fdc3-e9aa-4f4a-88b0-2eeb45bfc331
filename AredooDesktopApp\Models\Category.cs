using System.ComponentModel.DataAnnotations;

namespace AredooDesktopApp.Models;

public class Category
{
    public int Id { get; set; }
    
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [StringLength(100)]
    public string NameAr { get; set; } = string.Empty;
    
    [StringLength(500)]
    public string? Description { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    
    // Navigation property
    public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    
    // Calculated properties
    public string DisplayName => !string.IsNullOrEmpty(NameAr) ? NameAr : Name;
}
