package com.aredoo.desktop;

import com.aredoo.desktop.controller.LoginController;
import com.aredoo.desktop.model.User;
import com.aredoo.desktop.repository.UserRepository;
import javafx.application.Application;
import javafx.application.Platform;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.image.Image;
import javafx.stage.Stage;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.time.LocalDateTime;
import java.util.Locale;

@SpringBootApplication
public class AredooDesktopApplication extends Application {

    private ConfigurableApplicationContext springContext;
    private static ConfigurableApplicationContext staticSpringContext;
    private static String[] args;

    public static void main(String[] args) {
        AredooDesktopApplication.args = args;
        
        // Set Arabic locale
        Locale.setDefault(new Locale("ar", "IQ"));
        
        // Launch JavaFX application
        launch(args);
    }

    @Override
    public void init() throws Exception {
        // Start Spring Boot context
        springContext = SpringApplication.run(AredooDesktopApplication.class, args);
        staticSpringContext = springContext;

        // Initialize default users
        initializeDefaultUsers();
    }

    public static ConfigurableApplicationContext getSpringContext() {
        return staticSpringContext;
    }

    @Override
    public void start(Stage primaryStage) throws Exception {
        try {
            // Load login screen
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/login.fxml"));
            loader.setControllerFactory(springContext::getBean);
            
            Parent root = loader.load();
            
            // Set up the scene
            Scene scene = new Scene(root, 400, 300);
            scene.getStylesheets().add(getClass().getResource("/css/arabic-style.css").toExternalForm());
            
            // Configure stage
            primaryStage.setTitle("أريدوو - نظام إدارة المبيعات والمخزون");
            primaryStage.setScene(scene);
            primaryStage.setResizable(false);
            primaryStage.centerOnScreen();
            
            // Set application icon
            try {
                primaryStage.getIcons().add(new Image(getClass().getResourceAsStream("/images/icon.png")));
            } catch (Exception e) {
                System.out.println("Could not load application icon");
            }
            
            // Show the stage
            primaryStage.show();
            
            // Set the primary stage in login controller
            LoginController loginController = loader.getController();
            loginController.setPrimaryStage(primaryStage);
            
        } catch (Exception e) {
            e.printStackTrace();
            Platform.exit();
        }
    }

    @Override
    public void stop() throws Exception {
        if (springContext != null) {
            springContext.close();
        }
        Platform.exit();
    }

    private void initializeDefaultUsers() {
        try {
            UserRepository userRepository = springContext.getBean(UserRepository.class);
            BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
            
            // Check if admin user exists
            if (userRepository.findByUsername("admin").isEmpty()) {
                User admin = new User();
                admin.setUsername("admin");
                admin.setPassword(passwordEncoder.encode("1234"));
                admin.setFullName("المدير العام");
                admin.setRole("Admin");
                admin.setIsActive(true);
                admin.setCreatedAt(LocalDateTime.now());
                userRepository.save(admin);
                
                System.out.println("✅ تم إنشاء المستخدم الافتراضي: admin");
            }
            
            // Check if cashier user exists
            if (userRepository.findByUsername("cashier").isEmpty()) {
                User cashier = new User();
                cashier.setUsername("cashier");
                cashier.setPassword(passwordEncoder.encode("1234"));
                cashier.setFullName("الكاشير");
                cashier.setRole("Cashier");
                cashier.setIsActive(true);
                cashier.setCreatedAt(LocalDateTime.now());
                userRepository.save(cashier);
                
                System.out.println("✅ تم إنشاء المستخدم الافتراضي: cashier");
            }
            
        } catch (Exception e) {
            System.err.println("خطأ في إنشاء المستخدمين الافتراضيين: " + e.getMessage());
        }
    }
}
