using System.Globalization;

namespace AredooDesktopApp;

static class Program
{
    /// <summary>
    ///  The main entry point for the application.
    /// </summary>
    [STAThread]
    static void Main()
    {
        // Set Arabic culture for RTL support
        var culture = new CultureInfo("ar-SA");
        Thread.CurrentThread.CurrentCulture = culture;
        Thread.CurrentThread.CurrentUICulture = culture;

        // To customize application configuration such as set high DPI settings or default font,
        // see https://aka.ms/applicationconfiguration.
        ApplicationConfiguration.Initialize();

        try
        {
            Application.Run(new Form1());
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تشغيل التطبيق: {ex.Message}", "خطأ",
                          MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
}