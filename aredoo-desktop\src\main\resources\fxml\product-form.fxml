<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" 
      fx:controller="com.aredoo.desktop.controller.ProductFormController"
      spacing="15" styleClass="form-container">

   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>

   <!-- Header -->
   <Label fx:id="formTitle" text="إضافة منتج جديد" styleClass="section-title">
      <font>
         <Font name="Arial" size="18.0" />
      </font>
   </Label>

   <!-- Form Fields -->
   <GridPane hgap="15" vgap="15">
      <!-- Row 1 -->
      <VBox spacing="5" GridPane.columnIndex="0" GridPane.rowIndex="0" styleClass="form-field">
         <Label text="كود المنتج *:" styleClass="field-label"/>
         <TextField fx:id="codeField" promptText="أدخل كود المنتج" 
                   styleClass="text-field-arabic" prefWidth="200"/>
      </VBox>

      <VBox spacing="5" GridPane.columnIndex="1" GridPane.rowIndex="0" styleClass="form-field">
         <Label text="اسم المنتج (إنجليزي) *:" styleClass="field-label"/>
         <TextField fx:id="nameField" promptText="Product Name" 
                   styleClass="text-field-arabic" prefWidth="200"/>
      </VBox>

      <!-- Row 2 -->
      <VBox spacing="5" GridPane.columnIndex="0" GridPane.rowIndex="1" styleClass="form-field">
         <Label text="اسم المنتج (عربي) *:" styleClass="field-label"/>
         <TextField fx:id="nameArField" promptText="اسم المنتج بالعربية" 
                   styleClass="text-field-arabic" prefWidth="200"/>
      </VBox>

      <VBox spacing="5" GridPane.columnIndex="1" GridPane.rowIndex="1" styleClass="form-field">
         <Label text="التصنيف *:" styleClass="field-label"/>
         <ComboBox fx:id="categoryComboBox" editable="true" 
                  promptText="اختر أو أدخل التصنيف" prefWidth="200"/>
      </VBox>

      <!-- Row 3 -->
      <VBox spacing="5" GridPane.columnIndex="0" GridPane.rowIndex="2" styleClass="form-field">
         <Label text="الوحدة *:" styleClass="field-label"/>
         <ComboBox fx:id="unitComboBox" editable="true" 
                  promptText="اختر أو أدخل الوحدة" prefWidth="200"/>
      </VBox>

      <VBox spacing="5" GridPane.columnIndex="1" GridPane.rowIndex="2" styleClass="form-field">
         <Label text="الكمية الحالية:" styleClass="field-label"/>
         <TextField fx:id="quantityField" promptText="0" 
                   styleClass="text-field-arabic" prefWidth="200"/>
      </VBox>

      <!-- Row 4 -->
      <VBox spacing="5" GridPane.columnIndex="0" GridPane.rowIndex="3" styleClass="form-field">
         <Label text="سعر الشراء (د.ع) *:" styleClass="field-label"/>
         <TextField fx:id="purchasePriceField" promptText="0.00" 
                   styleClass="text-field-arabic" prefWidth="200"/>
      </VBox>

      <VBox spacing="5" GridPane.columnIndex="1" GridPane.rowIndex="3" styleClass="form-field">
         <Label text="سعر البيع (د.ع) *:" styleClass="field-label"/>
         <TextField fx:id="salePriceField" promptText="0.00" 
                   styleClass="text-field-arabic" prefWidth="200"/>
      </VBox>

      <!-- Row 5 -->
      <VBox spacing="5" GridPane.columnIndex="0" GridPane.rowIndex="4" styleClass="form-field">
         <Label text="الحد الأدنى للمخزون:" styleClass="field-label"/>
         <TextField fx:id="minStockField" promptText="0" 
                   styleClass="text-field-arabic" prefWidth="200"/>
      </VBox>

      <VBox spacing="5" GridPane.columnIndex="1" GridPane.rowIndex="4" styleClass="form-field">
         <Label text="الحد الأقصى للمخزون:" styleClass="field-label"/>
         <TextField fx:id="maxStockField" promptText="0" 
                   styleClass="text-field-arabic" prefWidth="200"/>
      </VBox>

      <!-- Row 6 - Description -->
      <VBox spacing="5" GridPane.columnIndex="0" GridPane.rowIndex="5" 
           GridPane.columnSpan="2" styleClass="form-field">
         <Label text="الوصف:" styleClass="field-label"/>
         <TextArea fx:id="descriptionArea" promptText="وصف المنتج (اختياري)" 
                  prefRowCount="3" styleClass="text-field-arabic"/>
      </VBox>

      <!-- Row 7 - Active Checkbox -->
      <VBox spacing="5" GridPane.columnIndex="0" GridPane.rowIndex="6" styleClass="form-field">
         <CheckBox fx:id="isActiveCheckBox" text="المنتج نشط" selected="true"/>
      </VBox>
   </GridPane>

   <!-- Error Label -->
   <Label fx:id="errorLabel" styleClass="error-label" visible="false"/>

   <!-- Buttons -->
   <HBox spacing="10" alignment="CENTER">
      <Button text="حفظ" onAction="#saveProduct" 
              styleClass="quick-action-button" prefWidth="100"/>
      <Button text="إلغاء" onAction="#cancel" 
              styleClass="button" prefWidth="100"/>
   </HBox>

</VBox>
