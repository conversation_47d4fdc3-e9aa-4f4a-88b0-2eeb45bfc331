using System.ComponentModel.DataAnnotations;

namespace AredooDesktopApp.Models;

public class Product
{
    public int Id { get; set; }
    
    [Required]
    [StringLength(50)]
    public string Code { get; set; } = string.Empty;
    
    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;
    
    [StringLength(200)]
    public string NameAr { get; set; } = string.Empty;
    
    [StringLength(100)]
    public string Category { get; set; } = string.Empty;
    
    [StringLength(20)]
    public string Unit { get; set; } = "قطعة";
    
    [Range(0, double.MaxValue)]
    public decimal PurchasePrice { get; set; }
    
    [Range(0, double.MaxValue)]
    public decimal SalePrice { get; set; }
    
    [Range(0, double.MaxValue)]
    public decimal WholesalePrice { get; set; }
    
    [Range(0, int.MaxValue)]
    public int Quantity { get; set; }
    
    [Range(0, int.MaxValue)]
    public int MinQuantity { get; set; } = 10;
    
    [StringLength(100)]
    public string? Barcode { get; set; }
    
    public string? Image { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    
    public DateTime? UpdatedAt { get; set; }
    
    // Calculated properties
    public decimal Profit => SalePrice - PurchasePrice;
    public bool IsLowStock => Quantity <= MinQuantity;
    public string DisplayName => !string.IsNullOrEmpty(NameAr) ? NameAr : Name;
}
