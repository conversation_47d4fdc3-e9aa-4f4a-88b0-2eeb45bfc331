Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' الحصول على مجلد التطبيق
strCurrentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

' تغيير المجلد الحالي
objShell.CurrentDirectory = strCurrentDir

' بناء التطبيق بصمت
objShell.Run "cmd /c dotnet build AredooDesktop --configuration Release --verbosity quiet", 0, True
objShell.Run "cmd /c dotnet build Aredoo.Server.csproj --configuration Release --verbosity quiet", 0, True

' تشغيل التطبيق
strExePath = strCurrentDir & "\AredooDesktop\bin\Release\net9.0-windows\AredooDesktop.exe"

If objFSO.FileExists(strExePath) Then
    objShell.Run """" & strExePath & """", 1, False
Else
    MsgBox "لم يتم العثور على ملف التطبيق. يرجى تشغيل StartAredooDesktop.bat أولاً.", vbCritical, "خطأ"
End If
