using System.ComponentModel.DataAnnotations;

namespace AredooDesktopApp.Models;

public class User
{
    public int Id { get; set; }
    
    [Required]
    [StringLength(50)]
    public string Username { get; set; } = string.Empty;
    
    [Required]
    public string PasswordHash { get; set; } = string.Empty;
    
    [Required]
    [StringLength(100)]
    public string FullName { get; set; } = string.Empty;
    
    [StringLength(20)]
    public string Role { get; set; } = "Cashier"; // <PERSON><PERSON>, Manager, Cashier, Employee
    
    public bool IsActive { get; set; } = true;
    
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    
    public DateTime? LastLogin { get; set; }
}
