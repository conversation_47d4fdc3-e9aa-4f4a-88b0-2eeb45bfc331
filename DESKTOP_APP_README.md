# أريدوو - تطبيق سطح المكتب
## Aredoo Desktop POS System

### نظرة عامة
تطبيق سطح مكتب لإدارة المبيعات والمخزون مبني بـ C# و Windows Forms باستخدام .NET 9.

### المميزات الرئيسية
- ✅ **إدارة المنتجات**: إضافة، تعديل، حذف، وعرض المنتجات
- ✅ **إدارة المخزون**: تتبع الكميات والحد الأدنى للمخزون
- ✅ **البحث المتقدم**: البحث في المنتجات بالاسم، الكود، أو التصنيف
- ✅ **قاعدة بيانات محلية**: SQLite لحفظ البيانات محلياً
- ✅ **واجهة عربية**: دعم كامل للغة العربية مع RTL
- ✅ **تنبيهات المخزون**: تنبيهات للمنتجات منخفضة المخزون

### متطلبات النظام
- Windows 10/11
- .NET 9 Runtime
- 100 MB مساحة فارغة على القرص الصلب

### طريقة التشغيل

#### الطريقة الأولى: استخدام ملف التشغيل
```bash
# انقر مرتين على الملف
StartAredooDesktopNew.bat
```

#### الطريقة الثانية: من سطر الأوامر
```bash
# بناء التطبيق
dotnet build AredooDesktopApp --configuration Release

# تشغيل التطبيق
AredooDesktopApp\bin\Release\net9.0-windows\AredooDesktopApp.exe
```

#### الطريقة الثالثة: تشغيل مباشر
```bash
dotnet run --project AredooDesktopApp
```

### بيانات الدخول الافتراضية
- **اسم المستخدم**: admin
- **كلمة المرور**: 1234

### هيكل المشروع
```
AredooDesktopApp/
├── Models/           # نماذج البيانات
│   ├── Product.cs    # نموذج المنتج
│   ├── User.cs       # نموذج المستخدم
│   └── Category.cs   # نموذج التصنيف
├── Data/             # طبقة البيانات
│   └── AredooDbContext.cs  # سياق قاعدة البيانات
├── Form1.cs          # النافذة الرئيسية
├── ProductForm.cs    # نافذة إدارة المنتجات
└── Program.cs        # نقطة دخول التطبيق
```

### قاعدة البيانات
- **النوع**: SQLite
- **الموقع**: `AredooDesktopApp/Data/aredoo.db`
- **الجداول**:
  - Products (المنتجات)
  - Users (المستخدمون)
  - Categories (التصنيفات)

### الميزات المتقدمة
1. **البحث الذكي**: البحث في جميع حقول المنتج
2. **التحقق من صحة البيانات**: التأكد من صحة البيانات المدخلة
3. **الحذف الآمن**: الحذف المنطقي للمنتجات
4. **تنسيق العملة**: عرض الأسعار بالتنسيق المناسب
5. **تنبيهات المخزون**: تمييز المنتجات منخفضة المخزون

### استكشاف الأخطاء

#### مشكلة: التطبيق لا يبدأ
**الحل**:
1. تأكد من تثبيت .NET 9
2. تشغيل كمدير إذا لزم الأمر
3. فحص Windows Defender/Antivirus

#### مشكلة: خطأ في قاعدة البيانات
**الحل**:
1. حذف ملف `aredoo.db` لإعادة إنشاء قاعدة البيانات
2. التأكد من صلاحيات الكتابة في مجلد التطبيق

#### مشكلة: النص العربي لا يظهر بشكل صحيح
**الحل**:
1. التأكد من إعدادات Windows للغة العربية
2. تثبيت خطوط عربية إضافية إذا لزم الأمر

### التطوير المستقبلي
- [ ] إضافة نظام المبيعات
- [ ] تقارير مفصلة
- [ ] نسخ احتياطي للبيانات
- [ ] إدارة المستخدمين والصلاحيات
- [ ] طباعة الفواتير
- [ ] تصدير البيانات (Excel, PDF)

### الدعم الفني
للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

---
**تم التطوير بواسطة**: Augment Agent  
**الإصدار**: 1.0.0  
**التاريخ**: نوفمبر 2025
