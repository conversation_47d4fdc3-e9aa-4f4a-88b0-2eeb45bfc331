@echo off
chcp 65001 > nul
title Aredoo POS System

echo.
echo ===============================================================
echo                    Aredoo POS System                  
echo                   Point of Sale and Inventory Management     
echo ===============================================================
echo.

cd /d "%~dp0"

echo Checking system requirements...
echo.

REM Check .NET
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: .NET is not installed or not available
    echo.
    echo Please download and install .NET 9 from:
    echo    https://dotnet.microsoft.com/download
    echo.
    pause
    exit /b 1
)

echo SUCCESS: .NET is available

REM Build the server application
echo Building server application...
dotnet build Aredoo.Server.csproj --configuration Release --verbosity quiet

if errorlevel 1 (
    echo ERROR: Failed to build server
    pause
    exit /b 1
)

echo SUCCESS: Server built successfully
echo.

echo Application Information:
echo    Name: Aredoo POS
echo    Version: 1.0.0
echo    Technology: .NET 9 + ASP.NET Core
echo    Database: SQLite (local)
echo    URL: http://localhost:5000
echo.

echo Default Login Credentials:
echo    Admin: admin / 1234
echo.

echo Starting server...
echo.

REM Start the server in background
start /B dotnet run --project Aredoo.Server.csproj

REM Wait for server to start
echo Waiting for server to start...
timeout /t 5 /nobreak >nul

REM Open browser
echo Opening browser...
start http://localhost:5000

echo.
echo SUCCESS: Application started!
echo.
echo The server is running in the background.
echo Open http://localhost:5000 in your browser if it didn't open automatically.
echo.
echo To stop the server, close this window or run StopAredoo.bat
echo.

REM Keep the window open
echo Press any key to stop the server...
pause >nul

REM Stop the server
echo Stopping server...
taskkill /f /im "dotnet.exe" >nul 2>&1
echo Server stopped.
