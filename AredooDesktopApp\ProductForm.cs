using AredooDesktopApp.Data;
using AredooDesktopApp.Models;
using System.Drawing.Drawing2D;

namespace AredooDesktopApp;

public partial class ProductForm : Form
{
    private AredooDbContext _context = null!;
    private Product? _product;
    private bool _isEditMode;

    // UI Controls
    private TextBox codeTextBox = null!;
    private TextBox nameTextBox = null!;
    private TextBox nameArTextBox = null!;
    private ComboBox categoryComboBox = null!;
    private TextBox unitTextBox = null!;
    private NumericUpDown purchasePriceNumeric = null!;
    private NumericUpDown salePriceNumeric = null!;
    private NumericUpDown wholesalePriceNumeric = null!;
    private NumericUpDown quantityNumeric = null!;
    private NumericUpDown minQuantityNumeric = null!;
    private TextBox barcodeTextBox = null!;
    private CheckBox isActiveCheckBox = null!;
    private Button saveButton = null!;
    private Button cancelButton = null!;

    public ProductForm(Product? product = null)
    {
        _context = new AredooDbContext();
        _product = product;
        _isEditMode = product != null;
        
        InitializeComponent();
        InitializeUI();
        LoadData();
    }

    private void InitializeUI()
    {
        // Form settings with modern styling
        this.Text = _isEditMode ? "✏️ تعديل منتج" : "➕ إضافة منتج جديد";
        this.Size = new Size(600, 700);
        this.StartPosition = FormStartPosition.CenterParent;
        this.FormBorderStyle = FormBorderStyle.FixedDialog;
        this.MaximizeBox = false;
        this.MinimizeBox = false;
        this.RightToLeft = RightToLeft.Yes;
        this.BackColor = Color.FromArgb(245, 247, 250);
        this.Font = new Font("Segoe UI", 9F);

        // Create main container panel
        var mainContainer = new Panel
        {
            Dock = DockStyle.Fill,
            Padding = new Padding(30),
            BackColor = Color.Transparent
        };
        this.Controls.Add(mainContainer);

        // Header panel
        var headerPanel = new Panel
        {
            Height = 60,
            Dock = DockStyle.Top,
            BackColor = Color.White
        };

        var headerLabel = new Label
        {
            Text = _isEditMode ? "✏️ تعديل بيانات المنتج" : "➕ إضافة منتج جديد",
            Font = new Font("Segoe UI", 14F, FontStyle.Bold),
            ForeColor = Color.FromArgb(52, 73, 94),
            Dock = DockStyle.Fill,
            TextAlign = ContentAlignment.MiddleCenter
        };

        headerPanel.Controls.Add(headerLabel);
        mainContainer.Controls.Add(headerPanel);

        // Content panel with scroll
        var contentPanel = new Panel
        {
            Dock = DockStyle.Fill,
            BackColor = Color.White,
            Padding = new Padding(20),
            AutoScroll = true
        };

        // Add shadow effect
        contentPanel.Paint += (s, e) => {
            var rect = new Rectangle(0, 0, contentPanel.Width, contentPanel.Height);
            using (var path = CreateRoundedRectanglePath(rect, 10))
            {
                e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
                e.Graphics.FillPath(new SolidBrush(Color.White), path);

                // Add subtle border
                e.Graphics.DrawPath(new Pen(Color.FromArgb(220, 220, 220), 1), path);
            }
        };

        mainContainer.Controls.Add(contentPanel);

        int yPos = 20;
        int labelWidth = 140;
        int controlWidth = 300;
        int spacing = 60;

        // Create input groups with modern styling
        var codeGroup = CreateInputGroup("🏷️ كود المنتج", yPos);
        codeTextBox = codeGroup.textBox;
        contentPanel.Controls.Add(codeGroup.container);
        yPos += spacing;

        var nameGroup = CreateInputGroup("📝 اسم المنتج (English)", yPos);
        nameTextBox = nameGroup.textBox;
        contentPanel.Controls.Add(nameGroup.container);
        yPos += spacing;

        var nameArGroup = CreateInputGroup("📝 اسم المنتج (العربية)", yPos);
        nameArTextBox = nameArGroup.textBox;
        contentPanel.Controls.Add(nameArGroup.container);
        yPos += spacing;

        var categoryGroup = CreateComboGroup("🏷️ التصنيف", yPos);
        categoryComboBox = categoryGroup.comboBox;
        contentPanel.Controls.Add(categoryGroup.container);
        yPos += spacing;

        var unitGroup = CreateInputGroup("📏 الوحدة", yPos);
        unitTextBox = unitGroup.textBox;
        unitTextBox.Text = "قطعة";
        contentPanel.Controls.Add(unitGroup.container);
        yPos += spacing;

        var purchasePriceGroup = CreateNumericGroup("💰 سعر الشراء", yPos, 999999, 2);
        purchasePriceNumeric = purchasePriceGroup.numericUpDown;
        contentPanel.Controls.Add(purchasePriceGroup.container);
        yPos += spacing;

        var salePriceGroup = CreateNumericGroup("💵 سعر البيع", yPos, 999999, 2);
        salePriceNumeric = salePriceGroup.numericUpDown;
        contentPanel.Controls.Add(salePriceGroup.container);
        yPos += spacing;

        var wholesalePriceGroup = CreateNumericGroup("🏪 سعر الجملة", yPos, 999999, 2);
        wholesalePriceNumeric = wholesalePriceGroup.numericUpDown;
        contentPanel.Controls.Add(wholesalePriceGroup.container);
        yPos += spacing;

        var quantityGroup = CreateNumericGroup("📦 الكمية", yPos, 999999, 0);
        quantityNumeric = quantityGroup.numericUpDown;
        contentPanel.Controls.Add(quantityGroup.container);
        yPos += spacing;

        var minQuantityGroup = CreateNumericGroup("⚠️ الحد الأدنى", yPos, 999999, 0);
        minQuantityNumeric = minQuantityGroup.numericUpDown;
        minQuantityNumeric.Value = 10;
        contentPanel.Controls.Add(minQuantityGroup.container);
        yPos += spacing;

        var barcodeGroup = CreateInputGroup("🏷️ الباركود", yPos);
        barcodeTextBox = barcodeGroup.textBox;
        contentPanel.Controls.Add(barcodeGroup.container);
        yPos += spacing;

        // Active status with modern styling
        var activeContainer = new Panel
        {
            Location = new Point(0, yPos),
            Size = new Size(500, 40),
            BackColor = Color.Transparent
        };

        isActiveCheckBox = new CheckBox
        {
            Text = "✅ المنتج نشط",
            Location = new Point(0, 10),
            Size = new Size(150, 25),
            Checked = true,
            Font = new Font("Segoe UI", 10F, FontStyle.Bold),
            ForeColor = Color.FromArgb(39, 174, 96)
        };

        activeContainer.Controls.Add(isActiveCheckBox);
        contentPanel.Controls.Add(activeContainer);
        yPos += 60;

        // Button panel
        var buttonPanel = new Panel
        {
            Location = new Point(0, yPos),
            Size = new Size(500, 60),
            BackColor = Color.Transparent
        };

        saveButton = CreateStyledButton("💾 حفظ", Color.FromArgb(39, 174, 96), Color.FromArgb(46, 204, 113));
        saveButton.Location = new Point(140, 10);
        saveButton.DialogResult = DialogResult.OK;
        saveButton.Click += SaveButton_Click;

        cancelButton = CreateStyledButton("❌ إلغاء", Color.FromArgb(231, 76, 60), Color.FromArgb(192, 57, 43));
        cancelButton.Location = new Point(270, 10);
        cancelButton.DialogResult = DialogResult.Cancel;

        buttonPanel.Controls.AddRange(new Control[] { saveButton, cancelButton });
        contentPanel.Controls.Add(buttonPanel);

        this.AcceptButton = saveButton;
        this.CancelButton = cancelButton;
    }

    private void LoadData()
    {
        // Load categories
        var categories = _context.Categories
            .Where(c => c.IsActive)
            .Select(c => c.DisplayName)
            .ToList();
        
        categoryComboBox.Items.AddRange(categories.ToArray());
        
        if (categoryComboBox.Items.Count > 0)
            categoryComboBox.SelectedIndex = 0;

        // Load product data if editing
        if (_isEditMode && _product != null)
        {
            codeTextBox.Text = _product.Code;
            nameTextBox.Text = _product.Name;
            nameArTextBox.Text = _product.NameAr;
            categoryComboBox.Text = _product.Category;
            unitTextBox.Text = _product.Unit;
            purchasePriceNumeric.Value = _product.PurchasePrice;
            salePriceNumeric.Value = _product.SalePrice;
            wholesalePriceNumeric.Value = _product.WholesalePrice;
            quantityNumeric.Value = _product.Quantity;
            minQuantityNumeric.Value = _product.MinQuantity;
            barcodeTextBox.Text = _product.Barcode ?? "";
            isActiveCheckBox.Checked = _product.IsActive;
        }
    }

    private void SaveButton_Click(object? sender, EventArgs e)
    {
        try
        {
            // Validation
            if (string.IsNullOrWhiteSpace(codeTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال كود المنتج", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                codeTextBox.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(nameTextBox.Text) && string.IsNullOrWhiteSpace(nameArTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المنتج", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                nameTextBox.Focus();
                return;
            }

            // Check for duplicate code
            var productId = _product?.Id ?? 0;
            var existingProduct = _context.Products
                .FirstOrDefault(p => p.Code == codeTextBox.Text && p.Id != productId);

            if (existingProduct != null)
            {
                MessageBox.Show("كود المنتج موجود مسبقاً", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                codeTextBox.Focus();
                return;
            }

            // Check for duplicate barcode
            if (!string.IsNullOrWhiteSpace(barcodeTextBox.Text))
            {
                var existingBarcode = _context.Products
                    .FirstOrDefault(p => p.Barcode == barcodeTextBox.Text && p.Id != productId);

                if (existingBarcode != null)
                {
                    MessageBox.Show("الباركود موجود مسبقاً", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    barcodeTextBox.Focus();
                    return;
                }
            }

            // Create or update product
            if (_isEditMode && _product != null)
            {
                // Update existing product
                _product.Code = codeTextBox.Text.Trim();
                _product.Name = nameTextBox.Text.Trim();
                _product.NameAr = nameArTextBox.Text.Trim();
                _product.Category = categoryComboBox.Text;
                _product.Unit = unitTextBox.Text.Trim();
                _product.PurchasePrice = purchasePriceNumeric.Value;
                _product.SalePrice = salePriceNumeric.Value;
                _product.WholesalePrice = wholesalePriceNumeric.Value;
                _product.Quantity = (int)quantityNumeric.Value;
                _product.MinQuantity = (int)minQuantityNumeric.Value;
                _product.Barcode = string.IsNullOrWhiteSpace(barcodeTextBox.Text) ? null : barcodeTextBox.Text.Trim();
                _product.IsActive = isActiveCheckBox.Checked;
                _product.UpdatedAt = DateTime.Now;
            }
            else
            {
                // Create new product
                var newProduct = new Product
                {
                    Code = codeTextBox.Text.Trim(),
                    Name = nameTextBox.Text.Trim(),
                    NameAr = nameArTextBox.Text.Trim(),
                    Category = categoryComboBox.Text,
                    Unit = unitTextBox.Text.Trim(),
                    PurchasePrice = purchasePriceNumeric.Value,
                    SalePrice = salePriceNumeric.Value,
                    WholesalePrice = wholesalePriceNumeric.Value,
                    Quantity = (int)quantityNumeric.Value,
                    MinQuantity = (int)minQuantityNumeric.Value,
                    Barcode = string.IsNullOrWhiteSpace(barcodeTextBox.Text) ? null : barcodeTextBox.Text.Trim(),
                    IsActive = isActiveCheckBox.Checked,
                    CreatedAt = DateTime.Now
                };

                _context.Products.Add(newProduct);
            }

            _context.SaveChanges();
            MessageBox.Show("تم حفظ المنتج بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            this.DialogResult = DialogResult.OK;
            this.Close();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حفظ المنتج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    protected override void OnFormClosed(FormClosedEventArgs e)
    {
        _context?.Dispose();
        base.OnFormClosed(e);
    }

    // Helper methods for modern UI components
    private (Panel container, TextBox textBox) CreateInputGroup(string labelText, int yPosition)
    {
        var container = new Panel
        {
            Location = new Point(0, yPosition),
            Size = new Size(500, 50),
            BackColor = Color.Transparent
        };

        var label = new Label
        {
            Text = labelText,
            Location = new Point(0, 0),
            Size = new Size(140, 20),
            Font = new Font("Segoe UI", 10F, FontStyle.Bold),
            ForeColor = Color.FromArgb(52, 73, 94)
        };

        var textBox = new TextBox
        {
            Location = new Point(0, 25),
            Size = new Size(400, 25),
            Font = new Font("Segoe UI", 10F),
            BorderStyle = BorderStyle.FixedSingle,
            BackColor = Color.White,
            ForeColor = Color.FromArgb(52, 73, 94)
        };

        // Add focus effects
        textBox.Enter += (s, e) => {
            textBox.BackColor = Color.FromArgb(240, 248, 255);
            textBox.BorderStyle = BorderStyle.FixedSingle;
        };
        textBox.Leave += (s, e) => {
            textBox.BackColor = Color.White;
        };

        container.Controls.AddRange(new Control[] { label, textBox });
        return (container, textBox);
    }

    private (Panel container, ComboBox comboBox) CreateComboGroup(string labelText, int yPosition)
    {
        var container = new Panel
        {
            Location = new Point(0, yPosition),
            Size = new Size(500, 50),
            BackColor = Color.Transparent
        };

        var label = new Label
        {
            Text = labelText,
            Location = new Point(0, 0),
            Size = new Size(140, 20),
            Font = new Font("Segoe UI", 10F, FontStyle.Bold),
            ForeColor = Color.FromArgb(52, 73, 94)
        };

        var comboBox = new ComboBox
        {
            Location = new Point(0, 25),
            Size = new Size(400, 25),
            Font = new Font("Segoe UI", 10F),
            DropDownStyle = ComboBoxStyle.DropDownList,
            BackColor = Color.White,
            ForeColor = Color.FromArgb(52, 73, 94)
        };

        container.Controls.AddRange(new Control[] { label, comboBox });
        return (container, comboBox);
    }

    private (Panel container, NumericUpDown numericUpDown) CreateNumericGroup(string labelText, int yPosition, decimal maximum = 999999, int decimalPlaces = 0)
    {
        var container = new Panel
        {
            Location = new Point(0, yPosition),
            Size = new Size(500, 50),
            BackColor = Color.Transparent
        };

        var label = new Label
        {
            Text = labelText,
            Location = new Point(0, 0),
            Size = new Size(140, 20),
            Font = new Font("Segoe UI", 10F, FontStyle.Bold),
            ForeColor = Color.FromArgb(52, 73, 94)
        };

        var numericUpDown = new NumericUpDown
        {
            Location = new Point(0, 25),
            Size = new Size(400, 25),
            Font = new Font("Segoe UI", 10F),
            Maximum = maximum,
            Minimum = 0,
            DecimalPlaces = decimalPlaces,
            BackColor = Color.White,
            ForeColor = Color.FromArgb(52, 73, 94)
        };

        container.Controls.AddRange(new Control[] { label, numericUpDown });
        return (container, numericUpDown);
    }

    private GraphicsPath CreateRoundedRectanglePath(Rectangle rect, int cornerRadius)
    {
        var path = new GraphicsPath();
        var diameter = cornerRadius * 2;

        path.AddArc(rect.X, rect.Y, diameter, diameter, 180, 90);
        path.AddArc(rect.Right - diameter, rect.Y, diameter, diameter, 270, 90);
        path.AddArc(rect.Right - diameter, rect.Bottom - diameter, diameter, diameter, 0, 90);
        path.AddArc(rect.X, rect.Bottom - diameter, diameter, diameter, 90, 90);
        path.CloseFigure();

        return path;
    }

    private Button CreateStyledButton(string text, Color backgroundColor, Color hoverColor)
    {
        var button = new Button
        {
            Text = text,
            Size = new Size(120, 45),
            FlatStyle = FlatStyle.Flat,
            BackColor = backgroundColor,
            ForeColor = Color.White,
            Font = new Font("Segoe UI", 10F, FontStyle.Bold),
            Cursor = Cursors.Hand
        };

        button.FlatAppearance.BorderSize = 0;
        button.FlatAppearance.MouseOverBackColor = hoverColor;

        return button;
    }
}
