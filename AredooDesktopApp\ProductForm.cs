using AredooDesktopApp.Data;
using AredooDesktopApp.Models;

namespace AredooDesktopApp;

public partial class ProductForm : Form
{
    private AredooDbContext _context = null!;
    private Product? _product;
    private bool _isEditMode;

    // UI Controls
    private TextBox codeTextBox = null!;
    private TextBox nameTextBox = null!;
    private TextBox nameArTextBox = null!;
    private ComboBox categoryComboBox = null!;
    private TextBox unitTextBox = null!;
    private NumericUpDown purchasePriceNumeric = null!;
    private NumericUpDown salePriceNumeric = null!;
    private NumericUpDown wholesalePriceNumeric = null!;
    private NumericUpDown quantityNumeric = null!;
    private NumericUpDown minQuantityNumeric = null!;
    private TextBox barcodeTextBox = null!;
    private CheckBox isActiveCheckBox = null!;
    private Button saveButton = null!;
    private Button cancelButton = null!;

    public ProductForm(Product? product = null)
    {
        _context = new AredooDbContext();
        _product = product;
        _isEditMode = product != null;
        
        InitializeComponent();
        InitializeUI();
        LoadData();
    }

    private void InitializeUI()
    {
        // Form settings
        this.Text = _isEditMode ? "تعديل منتج" : "إضافة منتج جديد";
        this.Size = new Size(500, 600);
        this.StartPosition = FormStartPosition.CenterParent;
        this.FormBorderStyle = FormBorderStyle.FixedDialog;
        this.MaximizeBox = false;
        this.MinimizeBox = false;
        this.RightToLeft = RightToLeft.Yes;

        int yPos = 20;
        int labelWidth = 120;
        int controlWidth = 200;
        int spacing = 35;

        // Code
        var codeLabel = new Label { Text = "كود المنتج:", Location = new Point(20, yPos), Size = new Size(labelWidth, 20) };
        codeTextBox = new TextBox { Location = new Point(150, yPos), Size = new Size(controlWidth, 25) };
        this.Controls.AddRange(new Control[] { codeLabel, codeTextBox });
        yPos += spacing;

        // Name
        var nameLabel = new Label { Text = "اسم المنتج (EN):", Location = new Point(20, yPos), Size = new Size(labelWidth, 20) };
        nameTextBox = new TextBox { Location = new Point(150, yPos), Size = new Size(controlWidth, 25) };
        this.Controls.AddRange(new Control[] { nameLabel, nameTextBox });
        yPos += spacing;

        // Name Arabic
        var nameArLabel = new Label { Text = "اسم المنتج (AR):", Location = new Point(20, yPos), Size = new Size(labelWidth, 20) };
        nameArTextBox = new TextBox { Location = new Point(150, yPos), Size = new Size(controlWidth, 25) };
        this.Controls.AddRange(new Control[] { nameArLabel, nameArTextBox });
        yPos += spacing;

        // Category
        var categoryLabel = new Label { Text = "التصنيف:", Location = new Point(20, yPos), Size = new Size(labelWidth, 20) };
        categoryComboBox = new ComboBox 
        { 
            Location = new Point(150, yPos), 
            Size = new Size(controlWidth, 25),
            DropDownStyle = ComboBoxStyle.DropDownList
        };
        this.Controls.AddRange(new Control[] { categoryLabel, categoryComboBox });
        yPos += spacing;

        // Unit
        var unitLabel = new Label { Text = "الوحدة:", Location = new Point(20, yPos), Size = new Size(labelWidth, 20) };
        unitTextBox = new TextBox { Location = new Point(150, yPos), Size = new Size(controlWidth, 25), Text = "قطعة" };
        this.Controls.AddRange(new Control[] { unitLabel, unitTextBox });
        yPos += spacing;

        // Purchase Price
        var purchasePriceLabel = new Label { Text = "سعر الشراء:", Location = new Point(20, yPos), Size = new Size(labelWidth, 20) };
        purchasePriceNumeric = new NumericUpDown 
        { 
            Location = new Point(150, yPos), 
            Size = new Size(controlWidth, 25),
            DecimalPlaces = 2,
            Maximum = 999999,
            Minimum = 0
        };
        this.Controls.AddRange(new Control[] { purchasePriceLabel, purchasePriceNumeric });
        yPos += spacing;

        // Sale Price
        var salePriceLabel = new Label { Text = "سعر البيع:", Location = new Point(20, yPos), Size = new Size(labelWidth, 20) };
        salePriceNumeric = new NumericUpDown 
        { 
            Location = new Point(150, yPos), 
            Size = new Size(controlWidth, 25),
            DecimalPlaces = 2,
            Maximum = 999999,
            Minimum = 0
        };
        this.Controls.AddRange(new Control[] { salePriceLabel, salePriceNumeric });
        yPos += spacing;

        // Wholesale Price
        var wholesalePriceLabel = new Label { Text = "سعر الجملة:", Location = new Point(20, yPos), Size = new Size(labelWidth, 20) };
        wholesalePriceNumeric = new NumericUpDown 
        { 
            Location = new Point(150, yPos), 
            Size = new Size(controlWidth, 25),
            DecimalPlaces = 2,
            Maximum = 999999,
            Minimum = 0
        };
        this.Controls.AddRange(new Control[] { wholesalePriceLabel, wholesalePriceNumeric });
        yPos += spacing;

        // Quantity
        var quantityLabel = new Label { Text = "الكمية:", Location = new Point(20, yPos), Size = new Size(labelWidth, 20) };
        quantityNumeric = new NumericUpDown 
        { 
            Location = new Point(150, yPos), 
            Size = new Size(controlWidth, 25),
            Maximum = 999999,
            Minimum = 0
        };
        this.Controls.AddRange(new Control[] { quantityLabel, quantityNumeric });
        yPos += spacing;

        // Min Quantity
        var minQuantityLabel = new Label { Text = "الحد الأدنى:", Location = new Point(20, yPos), Size = new Size(labelWidth, 20) };
        minQuantityNumeric = new NumericUpDown 
        { 
            Location = new Point(150, yPos), 
            Size = new Size(controlWidth, 25),
            Maximum = 999999,
            Minimum = 0,
            Value = 10
        };
        this.Controls.AddRange(new Control[] { minQuantityLabel, minQuantityNumeric });
        yPos += spacing;

        // Barcode
        var barcodeLabel = new Label { Text = "الباركود:", Location = new Point(20, yPos), Size = new Size(labelWidth, 20) };
        barcodeTextBox = new TextBox { Location = new Point(150, yPos), Size = new Size(controlWidth, 25) };
        this.Controls.AddRange(new Control[] { barcodeLabel, barcodeTextBox });
        yPos += spacing;

        // Is Active
        isActiveCheckBox = new CheckBox 
        { 
            Text = "نشط", 
            Location = new Point(150, yPos), 
            Size = new Size(100, 25),
            Checked = true
        };
        this.Controls.Add(isActiveCheckBox);
        yPos += spacing + 20;

        // Buttons
        saveButton = new Button
        {
            Text = "حفظ",
            Location = new Point(150, yPos),
            Size = new Size(80, 35),
            BackColor = Color.LightGreen,
            DialogResult = DialogResult.OK
        };
        saveButton.Click += SaveButton_Click;

        cancelButton = new Button
        {
            Text = "إلغاء",
            Location = new Point(240, yPos),
            Size = new Size(80, 35),
            BackColor = Color.LightCoral,
            DialogResult = DialogResult.Cancel
        };

        this.Controls.AddRange(new Control[] { saveButton, cancelButton });
        this.AcceptButton = saveButton;
        this.CancelButton = cancelButton;
    }

    private void LoadData()
    {
        // Load categories
        var categories = _context.Categories
            .Where(c => c.IsActive)
            .Select(c => c.DisplayName)
            .ToList();
        
        categoryComboBox.Items.AddRange(categories.ToArray());
        
        if (categoryComboBox.Items.Count > 0)
            categoryComboBox.SelectedIndex = 0;

        // Load product data if editing
        if (_isEditMode && _product != null)
        {
            codeTextBox.Text = _product.Code;
            nameTextBox.Text = _product.Name;
            nameArTextBox.Text = _product.NameAr;
            categoryComboBox.Text = _product.Category;
            unitTextBox.Text = _product.Unit;
            purchasePriceNumeric.Value = _product.PurchasePrice;
            salePriceNumeric.Value = _product.SalePrice;
            wholesalePriceNumeric.Value = _product.WholesalePrice;
            quantityNumeric.Value = _product.Quantity;
            minQuantityNumeric.Value = _product.MinQuantity;
            barcodeTextBox.Text = _product.Barcode ?? "";
            isActiveCheckBox.Checked = _product.IsActive;
        }
    }

    private void SaveButton_Click(object? sender, EventArgs e)
    {
        try
        {
            // Validation
            if (string.IsNullOrWhiteSpace(codeTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال كود المنتج", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                codeTextBox.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(nameTextBox.Text) && string.IsNullOrWhiteSpace(nameArTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المنتج", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                nameTextBox.Focus();
                return;
            }

            // Check for duplicate code
            var productId = _product?.Id ?? 0;
            var existingProduct = _context.Products
                .FirstOrDefault(p => p.Code == codeTextBox.Text && p.Id != productId);

            if (existingProduct != null)
            {
                MessageBox.Show("كود المنتج موجود مسبقاً", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                codeTextBox.Focus();
                return;
            }

            // Check for duplicate barcode
            if (!string.IsNullOrWhiteSpace(barcodeTextBox.Text))
            {
                var existingBarcode = _context.Products
                    .FirstOrDefault(p => p.Barcode == barcodeTextBox.Text && p.Id != productId);

                if (existingBarcode != null)
                {
                    MessageBox.Show("الباركود موجود مسبقاً", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    barcodeTextBox.Focus();
                    return;
                }
            }

            // Create or update product
            if (_isEditMode && _product != null)
            {
                // Update existing product
                _product.Code = codeTextBox.Text.Trim();
                _product.Name = nameTextBox.Text.Trim();
                _product.NameAr = nameArTextBox.Text.Trim();
                _product.Category = categoryComboBox.Text;
                _product.Unit = unitTextBox.Text.Trim();
                _product.PurchasePrice = purchasePriceNumeric.Value;
                _product.SalePrice = salePriceNumeric.Value;
                _product.WholesalePrice = wholesalePriceNumeric.Value;
                _product.Quantity = (int)quantityNumeric.Value;
                _product.MinQuantity = (int)minQuantityNumeric.Value;
                _product.Barcode = string.IsNullOrWhiteSpace(barcodeTextBox.Text) ? null : barcodeTextBox.Text.Trim();
                _product.IsActive = isActiveCheckBox.Checked;
                _product.UpdatedAt = DateTime.Now;
            }
            else
            {
                // Create new product
                var newProduct = new Product
                {
                    Code = codeTextBox.Text.Trim(),
                    Name = nameTextBox.Text.Trim(),
                    NameAr = nameArTextBox.Text.Trim(),
                    Category = categoryComboBox.Text,
                    Unit = unitTextBox.Text.Trim(),
                    PurchasePrice = purchasePriceNumeric.Value,
                    SalePrice = salePriceNumeric.Value,
                    WholesalePrice = wholesalePriceNumeric.Value,
                    Quantity = (int)quantityNumeric.Value,
                    MinQuantity = (int)minQuantityNumeric.Value,
                    Barcode = string.IsNullOrWhiteSpace(barcodeTextBox.Text) ? null : barcodeTextBox.Text.Trim(),
                    IsActive = isActiveCheckBox.Checked,
                    CreatedAt = DateTime.Now
                };

                _context.Products.Add(newProduct);
            }

            _context.SaveChanges();
            MessageBox.Show("تم حفظ المنتج بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            this.DialogResult = DialogResult.OK;
            this.Close();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حفظ المنتج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    protected override void OnFormClosed(FormClosedEventArgs e)
    {
        _context?.Dispose();
        base.OnFormClosed(e);
    }
}
