@echo off
chcp 65001 > nul
title Aredoo Desktop - Quick Setup

echo.
echo ===============================================================
echo                    Aredoo Desktop POS System                  
echo                        Quick Setup Wizard
echo ===============================================================
echo.

cd /d "%~dp0"

echo [1/4] Checking system requirements...

REM Check .NET
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: .NET 9 is not installed
    echo.
    echo Please install .NET 9 from: https://dotnet.microsoft.com/download
    echo After installation, run this setup again.
    echo.
    pause
    exit /b 1
) else (
    echo ✅ .NET 9 is installed
)

echo.
echo [2/4] Building application...

dotnet build AredooDesktopApp --configuration Release --verbosity minimal

if errorlevel 1 (
    echo ❌ ERROR: Failed to build application
    echo.
    echo Please check the error messages above and try again.
    pause
    exit /b 1
) else (
    echo ✅ Application built successfully
)

echo.
echo [3/4] Setting up database...

REM Create Data directory if it doesn't exist
if not exist "AredooDesktopApp\bin\Release\net9.0-windows\Data" (
    mkdir "AredooDesktopApp\bin\Release\net9.0-windows\Data"
)

echo ✅ Database setup completed

echo.
echo [4/4] Creating shortcuts...

REM Create desktop shortcut (optional)
set /p createShortcut="Create desktop shortcut? (y/n): "
if /i "%createShortcut%"=="y" (
    echo Creating desktop shortcut...
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Aredoo POS.lnk'); $Shortcut.TargetPath = '%CD%\AredooDesktopApp\bin\Release\net9.0-windows\AredooDesktopApp.exe'; $Shortcut.WorkingDirectory = '%CD%'; $Shortcut.Description = 'Aredoo Desktop POS System'; $Shortcut.Save()"
    echo ✅ Desktop shortcut created
) else (
    echo ⏭️ Skipped desktop shortcut
)

echo.
echo ===============================================================
echo                        Setup Complete!                        
echo ===============================================================
echo.

echo 🎉 Aredoo Desktop POS System is ready to use!
echo.
echo Quick Start:
echo   • Run: StartAredooDesktopNew.bat
echo   • Or double-click: AredooDesktopApp\bin\Release\net9.0-windows\AredooDesktopApp.exe
echo.
echo Default Login:
echo   • Username: admin
echo   • Password: 1234
echo.
echo Features:
echo   ✅ Product Management
echo   ✅ Inventory Control  
echo   ✅ Arabic Interface
echo   ✅ Search & Filter
echo   ✅ Low Stock Alerts
echo.

set /p startNow="Start the application now? (y/n): "
if /i "%startNow%"=="y" (
    echo.
    echo Starting Aredoo Desktop POS...
    start "" "AredooDesktopApp\bin\Release\net9.0-windows\AredooDesktopApp.exe"
    echo.
    echo Application started! Check for the new window.
) else (
    echo.
    echo You can start the application later using StartAredooDesktopNew.bat
)

echo.
echo Thank you for using Aredoo Desktop POS System!
echo.
pause
