# أريدوو - تطبيق سطح المكتب
## Aredoo Desktop POS Application

### 📋 نظرة عامة | Overview

**أريدوو** هو نظام إدارة المبيعات والمخزون مصمم خصيصاً للشركات والمتاجر العربية. هذا الإصدار هو تطبيق سطح مكتب حقيقي يعمل على Windows.

**Aredoo** is a Point of Sale and Inventory Management system designed specifically for Arabic businesses. This version is a true desktop application for Windows.

### ✨ المميزات | Features

- 🖥️ **تطبيق سطح مكتب حقيقي** - لا يحتاج متصفح
- 🏪 **إدارة المنتجات** - إضافة وتعديل وحذف المنتجات مع التصنيفات
- 📊 **إدارة المخزون** - تتبع الكميات والتنبيهات عند نفاد المخزون
- 🧾 **نظام الفواتير** - إنشاء فواتير المبيعات والمشتريات
- 👥 **إدارة العملاء** - قاعدة بيانات العملاء والموردين
- 📈 **التقارير** - تقارير مفصلة للمبيعات والمخزون والأرباح
- 🔐 **نظام المستخدمين** - مستويات صلاحيات متعددة
- 🌐 **واجهة عربية** - دعم كامل للغة العربية و RTL
- 💾 **قاعدة بيانات محلية** - لا يتطلب اتصال بالإنترنت

### 🛠️ التقنيات المستخدمة | Technologies Used

- **.NET 9** - إطار العمل الأساسي
- **Windows Forms** - واجهة سطح المكتب
- **WebView2** - عرض الواجهة الويب
- **ASP.NET Core** - الخادم المحلي
- **SQLite** - قاعدة البيانات المحلية
- **Entity Framework Core** - طبقة الوصول للبيانات

### 📋 المتطلبات | Requirements

- **Windows 10/11** (64-bit)
- **.NET 9 Runtime** (سيتم تثبيته تلقائياً)
- **Microsoft Edge WebView2** (مثبت مسبقاً في Windows 11)
- **4GB RAM** (الحد الأدنى)
- **1GB** مساحة فارغة على القرص

### 🚀 التشغيل السريع | Quick Start

#### الطريقة الأولى - ملف التشغيل (الأسهل):
```bash
# انقر مرتين على الملف
StartAredooDesktop.bat
```

#### الطريقة الثانية - التشغيل الصامت:
```bash
# انقر مرتين على الملف (بدون نافذة سوداء)
StartAredooDesktop.vbs
```

#### الطريقة الثالثة - يدوياً:
```bash
# بناء التطبيق
dotnet build AredooDesktop --configuration Release

# تشغيل التطبيق
AredooDesktop\bin\Release\net9.0-windows\AredooDesktop.exe
```

### 👤 بيانات الدخول الافتراضية | Default Login

| المستخدم | كلمة المرور | الصلاحية |
|----------|-------------|----------|
| admin    | 1234        | مدير     |

### 🔧 حل المشاكل الشائعة | Troubleshooting

#### المشكلة: التطبيق لا يبدأ
**الحل**:
1. تأكد من تثبيت .NET 9
2. تأكد من تثبيت Microsoft Edge WebView2
3. شغل التطبيق كمدير (Run as Administrator)

#### المشكلة: "WebView2 Runtime not found"
**الحل**:
1. حمل WebView2 من: https://developer.microsoft.com/microsoft-edge/webview2/
2. ثبت النسخة "Evergreen Standalone Installer"

#### المشكلة: الخادم لا يبدأ
**الحل**:
1. تحقق من أن المنفذ 5000 غير مستخدم
2. تحقق من إعدادات جدار الحماية
3. شغل التطبيق كمدير

### 📁 هيكل المشروع | Project Structure

```
pos/
├── AredooDesktop/              # تطبيق سطح المكتب
│   ├── Form1.cs               # النافذة الرئيسية
│   ├── Program.cs             # نقطة البداية
│   └── bin/Release/           # الملفات المبنية
├── Data/                      # قاعدة البيانات
├── wwwroot/                   # الواجهة الأمامية
├── StartAredooDesktop.bat     # ملف التشغيل
├── StartAredooDesktop.vbs     # التشغيل الصامت
└── StopAredooDesktop.bat      # ملف الإيقاف
```

### 🎯 الاستخدام | Usage

1. **تشغيل التطبيق**: انقر على `StartAredooDesktop.bat`
2. **تسجيل الدخول**: استخدم admin/1234
3. **إدارة المنتجات**: من القائمة الجانبية
4. **إنشاء فاتورة**: من نقطة البيع
5. **عرض التقارير**: من قسم التقارير
6. **إيقاف التطبيق**: انقر على `StopAredooDesktop.bat`

### 🔄 التحديثات | Updates

للحصول على آخر التحديثات:
1. احتفظ بنسخة احتياطية من مجلد `Data/`
2. حمل الإصدار الجديد
3. انسخ مجلد `Data/` إلى المكان الجديد

### 🆘 الدعم الفني | Technical Support

للدعم والاستفسارات:
- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: www.aredoo.com

---

**تطوير**: فريق أريدوو | **Developed by**: Aredoo Team  
**الإصدار**: 1.0.0 | **Version**: 1.0.0
