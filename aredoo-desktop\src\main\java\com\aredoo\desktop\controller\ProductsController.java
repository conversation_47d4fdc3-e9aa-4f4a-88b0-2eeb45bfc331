package com.aredoo.desktop.controller;

import com.aredoo.desktop.model.Product;
import com.aredoo.desktop.repository.ProductRepository;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.stage.Modality;
import javafx.stage.Stage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Controller
public class ProductsController {

    @FXML private TextField searchField;
    @FXML private ComboBox<String> categoryComboBox;
    @FXML private TableView<Product> productsTable;
    @FXML private TableColumn<Product, String> codeColumn;
    @FXML private TableColumn<Product, String> nameColumn;
    @FXML private TableColumn<Product, String> nameArColumn;
    @FXML private TableColumn<Product, String> categoryColumn;
    @FXML private TableColumn<Product, String> unitColumn;
    @FXML private TableColumn<Product, BigDecimal> purchasePriceColumn;
    @FXML private TableColumn<Product, BigDecimal> salePriceColumn;
    @FXML private TableColumn<Product, Integer> quantityColumn;
    @FXML private TableColumn<Product, Void> actionsColumn;
    @FXML private Label statusLabel;

    @Autowired
    private ProductRepository productRepository;

    private ObservableList<Product> productsList = FXCollections.observableArrayList();

    @FXML
    private void initialize() {
        setupTableColumns();
        loadProducts();
        loadCategories();
        setupSearchListener();
    }

    private void setupTableColumns() {
        codeColumn.setCellValueFactory(new PropertyValueFactory<>("code"));
        nameColumn.setCellValueFactory(new PropertyValueFactory<>("name"));
        nameArColumn.setCellValueFactory(new PropertyValueFactory<>("nameAr"));
        categoryColumn.setCellValueFactory(new PropertyValueFactory<>("category"));
        unitColumn.setCellValueFactory(new PropertyValueFactory<>("unit"));
        purchasePriceColumn.setCellValueFactory(new PropertyValueFactory<>("purchasePrice"));
        salePriceColumn.setCellValueFactory(new PropertyValueFactory<>("salePrice"));
        quantityColumn.setCellValueFactory(new PropertyValueFactory<>("quantity"));

        // Format price columns
        purchasePriceColumn.setCellFactory(column -> new TableCell<Product, BigDecimal>() {
            @Override
            protected void updateItem(BigDecimal price, boolean empty) {
                super.updateItem(price, empty);
                if (empty || price == null) {
                    setText(null);
                } else {
                    setText(String.format("%.2f د.ع", price.doubleValue()));
                }
            }
        });

        salePriceColumn.setCellFactory(column -> new TableCell<Product, BigDecimal>() {
            @Override
            protected void updateItem(BigDecimal price, boolean empty) {
                super.updateItem(price, empty);
                if (empty || price == null) {
                    setText(null);
                } else {
                    setText(String.format("%.2f د.ع", price.doubleValue()));
                }
            }
        });

        // Setup actions column
        actionsColumn.setCellFactory(column -> new TableCell<Product, Void>() {
            private final Button editButton = new Button("تعديل");
            private final Button deleteButton = new Button("حذف");

            {
                editButton.setOnAction(e -> {
                    Product product = getTableView().getItems().get(getIndex());
                    editProduct(product);
                });

                deleteButton.setOnAction(e -> {
                    Product product = getTableView().getItems().get(getIndex());
                    deleteProduct(product);
                });

                editButton.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-size: 10px;");
                deleteButton.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-size: 10px;");
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    setGraphic(new javafx.scene.layout.HBox(5, editButton, deleteButton));
                }
            }
        });

        productsTable.setItems(productsList);
    }

    private void setupSearchListener() {
        searchField.textProperty().addListener((observable, oldValue, newValue) -> {
            if (newValue.length() >= 2 || newValue.isEmpty()) {
                searchProducts();
            }
        });
    }

    private void loadProducts() {
        try {
            List<Product> products = productRepository.findByIsActiveTrueOrderByName();
            productsList.clear();
            productsList.addAll(products);
            updateStatusLabel(products.size() + " منتج");
        } catch (Exception e) {
            showError("خطأ في تحميل المنتجات: " + e.getMessage());
        }
    }

    private void loadCategories() {
        try {
            List<String> categories = productRepository.findAllCategories();
            categoryComboBox.getItems().clear();
            categoryComboBox.getItems().add("جميع التصنيفات");
            categoryComboBox.getItems().addAll(categories);
            categoryComboBox.setValue("جميع التصنيفات");
        } catch (Exception e) {
            System.err.println("خطأ في تحميل التصنيفات: " + e.getMessage());
        }
    }

    @FXML
    private void addNewProduct() {
        openProductForm(null);
    }

    @FXML
    private void editSelectedProduct() {
        Product selected = productsTable.getSelectionModel().getSelectedItem();
        if (selected != null) {
            editProduct(selected);
        } else {
            showWarning("يرجى اختيار منتج للتعديل");
        }
    }

    @FXML
    private void deleteSelectedProduct() {
        Product selected = productsTable.getSelectionModel().getSelectedItem();
        if (selected != null) {
            deleteProduct(selected);
        } else {
            showWarning("يرجى اختيار منتج للحذف");
        }
    }

    @FXML
    private void searchProducts() {
        try {
            String searchTerm = searchField.getText().trim();
            String selectedCategory = categoryComboBox.getValue();
            
            List<Product> products;
            
            if (searchTerm.isEmpty() && "جميع التصنيفات".equals(selectedCategory)) {
                products = productRepository.findByIsActiveTrueOrderByName();
            } else if (!searchTerm.isEmpty() && "جميع التصنيفات".equals(selectedCategory)) {
                products = productRepository.searchProducts(searchTerm);
            } else if (searchTerm.isEmpty() && !"جميع التصنيفات".equals(selectedCategory)) {
                products = productRepository.findByCategoryAndIsActiveTrueOrderByName(selectedCategory);
            } else {
                products = productRepository.searchProductsByCategory(searchTerm, selectedCategory);
            }
            
            productsList.clear();
            productsList.addAll(products);
            updateStatusLabel("تم العثور على " + products.size() + " منتج");
            
        } catch (Exception e) {
            showError("خطأ في البحث: " + e.getMessage());
        }
    }

    @FXML
    private void resetSearch() {
        searchField.clear();
        categoryComboBox.setValue("جميع التصنيفات");
        loadProducts();
    }

    @FXML
    private void exportToExcel() {
        // TODO: Implement Excel export
        showInfo("سيتم تنفيذ هذه الميزة قريباً");
    }

    private void editProduct(Product product) {
        openProductForm(product);
    }

    private void deleteProduct(Product product) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("تأكيد الحذف");
        alert.setHeaderText("حذف المنتج");
        alert.setContentText("هل أنت متأكد من حذف المنتج: " + product.getNameAr() + "؟");

        Optional<ButtonType> result = alert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            try {
                product.setIsActive(false);
                productRepository.save(product);
                loadProducts();
                showInfo("تم حذف المنتج بنجاح");
            } catch (Exception e) {
                showError("خطأ في حذف المنتج: " + e.getMessage());
            }
        }
    }

    private void openProductForm(Product product) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/product-form.fxml"));
            Parent root = loader.load();
            
            // Pass product to form controller if editing
            // ProductFormController controller = loader.getController();
            // if (product != null) {
            //     controller.setProduct(product);
            // }
            
            Stage stage = new Stage();
            stage.setTitle(product == null ? "إضافة منتج جديد" : "تعديل المنتج");
            stage.setScene(new Scene(root, 600, 500));
            stage.initModality(Modality.APPLICATION_MODAL);
            stage.centerOnScreen();
            stage.showAndWait();
            
            // Refresh products list after form closes
            loadProducts();
            
        } catch (Exception e) {
            showError("خطأ في فتح نموذج المنتج: " + e.getMessage());
        }
    }

    private void updateStatusLabel(String message) {
        statusLabel.setText(message);
    }

    private void showError(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("خطأ");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showWarning(String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("تحذير");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showInfo(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("معلومات");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
