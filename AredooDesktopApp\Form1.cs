using AredooDesktopApp.Data;
using AredooDesktopApp.Models;
using Microsoft.EntityFrameworkCore;

namespace AredooDesktopApp;

public partial class Form1 : Form
{
    private AredooDbContext _context = null!;
    private User? _currentUser;

    // UI Controls
    private MenuStrip menuStrip = null!;
    private StatusStrip statusStrip = null!;
    private ToolStripStatusLabel statusLabel = null!;
    private Panel mainPanel = null!;
    private DataGridView dataGridView = null!;
    private Panel buttonPanel = null!;
    private Button addButton = null!;
    private Button editButton = null!;
    private Button deleteButton = null!;
    private Button refreshButton = null!;
    private TextBox searchTextBox = null!;
    private Label searchLabel = null!;

    public Form1()
    {
        InitializeComponent();
        InitializeDatabase();
        InitializeUI();
        LoadProducts();
    }

    private void InitializeDatabase()
    {
        _context = new AredooDbContext();
        _context.Database.EnsureCreated();

        // Login with default admin user for now
        _currentUser = _context.Users.FirstOrDefault(u => u.Username == "admin");
    }

    private void InitializeUI()
    {
        // Form settings
        this.Text = "أريدوو - نظام إدارة المبيعات والمخزون";
        this.Size = new Size(1200, 800);
        this.StartPosition = FormStartPosition.CenterScreen;
        this.WindowState = FormWindowState.Maximized;

        // Menu Strip
        menuStrip = new MenuStrip();
        var fileMenu = new ToolStripMenuItem("ملف");
        var productsMenu = new ToolStripMenuItem("المنتجات");
        var categoriesMenu = new ToolStripMenuItem("التصنيفات");
        var reportsMenu = new ToolStripMenuItem("التقارير");
        var helpMenu = new ToolStripMenuItem("مساعدة");

        fileMenu.DropDownItems.Add("خروج", null, (s, e) => Application.Exit());
        productsMenu.DropDownItems.Add("عرض المنتجات", null, (s, e) => LoadProducts());
        productsMenu.DropDownItems.Add("إضافة منتج", null, (s, e) => AddProduct());

        menuStrip.Items.AddRange(new[] { fileMenu, productsMenu, categoriesMenu, reportsMenu, helpMenu });
        this.MainMenuStrip = menuStrip;
        this.Controls.Add(menuStrip);

        // Status Strip
        statusStrip = new StatusStrip();
        statusLabel = new ToolStripStatusLabel($"مرحباً {_currentUser?.FullName ?? "مستخدم"}");
        statusStrip.Items.Add(statusLabel);
        this.Controls.Add(statusStrip);

        // Main Panel
        mainPanel = new Panel
        {
            Dock = DockStyle.Fill,
            Padding = new Padding(10)
        };
        this.Controls.Add(mainPanel);

        // Search Panel
        var searchPanel = new Panel
        {
            Height = 50,
            Dock = DockStyle.Top
        };

        searchLabel = new Label
        {
            Text = "البحث:",
            Location = new Point(10, 15),
            Size = new Size(50, 20)
        };

        searchTextBox = new TextBox
        {
            Location = new Point(70, 12),
            Size = new Size(200, 25)
        };
        searchTextBox.TextChanged += SearchTextBox_TextChanged;

        searchPanel.Controls.AddRange(new Control[] { searchLabel, searchTextBox });
        mainPanel.Controls.Add(searchPanel);

        // Button Panel
        buttonPanel = new Panel
        {
            Height = 50,
            Dock = DockStyle.Top
        };

        addButton = new Button
        {
            Text = "إضافة منتج",
            Location = new Point(10, 10),
            Size = new Size(100, 30),
            BackColor = Color.LightGreen
        };
        addButton.Click += (s, e) => AddProduct();

        editButton = new Button
        {
            Text = "تعديل",
            Location = new Point(120, 10),
            Size = new Size(80, 30),
            BackColor = Color.LightBlue
        };
        editButton.Click += (s, e) => EditProduct();

        deleteButton = new Button
        {
            Text = "حذف",
            Location = new Point(210, 10),
            Size = new Size(80, 30),
            BackColor = Color.LightCoral
        };
        deleteButton.Click += (s, e) => DeleteProduct();

        refreshButton = new Button
        {
            Text = "تحديث",
            Location = new Point(300, 10),
            Size = new Size(80, 30),
            BackColor = Color.LightYellow
        };
        refreshButton.Click += (s, e) => LoadProducts();

        buttonPanel.Controls.AddRange(new Control[] { addButton, editButton, deleteButton, refreshButton });
        mainPanel.Controls.Add(buttonPanel);

        // DataGridView
        dataGridView = new DataGridView
        {
            Dock = DockStyle.Fill,
            AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            MultiSelect = false,
            ReadOnly = true,
            AllowUserToAddRows = false,
            AllowUserToDeleteRows = false,
            RightToLeft = RightToLeft.Yes
        };

        mainPanel.Controls.Add(dataGridView);
    }

    private void LoadProducts()
    {
        try
        {
            var products = _context.Products
                .Where(p => p.IsActive)
                .OrderBy(p => p.Name)
                .Select(p => new
                {
                    p.Id,
                    الكود = p.Code,
                    الاسم = p.DisplayName,
                    التصنيف = p.Category,
                    الوحدة = p.Unit,
                    سعر_الشراء = p.PurchasePrice,
                    سعر_البيع = p.SalePrice,
                    الكمية = p.Quantity,
                    الحد_الأدنى = p.MinQuantity,
                    حالة_المخزون = p.IsLowStock ? "منخفض" : "طبيعي"
                })
                .ToList();

            dataGridView.DataSource = products;

            // Hide ID column
            if (dataGridView.Columns["Id"] != null)
                dataGridView.Columns["Id"].Visible = false;

            // Color low stock rows
            foreach (DataGridViewRow row in dataGridView.Rows)
            {
                if (row.Cells["حالة_المخزون"].Value?.ToString() == "منخفض")
                {
                    row.DefaultCellStyle.BackColor = Color.LightPink;
                }
            }

            statusLabel.Text = $"تم تحميل {products.Count} منتج";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void SearchTextBox_TextChanged(object? sender, EventArgs e)
    {
        if (string.IsNullOrWhiteSpace(searchTextBox.Text))
        {
            LoadProducts();
            return;
        }

        try
        {
            var searchTerm = searchTextBox.Text.ToLower();
            var products = _context.Products
                .Where(p => p.IsActive &&
                           (p.Name.ToLower().Contains(searchTerm) ||
                            p.NameAr.ToLower().Contains(searchTerm) ||
                            p.Code.ToLower().Contains(searchTerm) ||
                            p.Category.ToLower().Contains(searchTerm)))
                .OrderBy(p => p.Name)
                .Select(p => new
                {
                    p.Id,
                    الكود = p.Code,
                    الاسم = p.DisplayName,
                    التصنيف = p.Category,
                    الوحدة = p.Unit,
                    سعر_الشراء = p.PurchasePrice,
                    سعر_البيع = p.SalePrice,
                    الكمية = p.Quantity,
                    الحد_الأدنى = p.MinQuantity,
                    حالة_المخزون = p.IsLowStock ? "منخفض" : "طبيعي"
                })
                .ToList();

            dataGridView.DataSource = products;

            if (dataGridView.Columns["Id"] != null)
                dataGridView.Columns["Id"].Visible = false;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void AddProduct()
    {
        var addForm = new ProductForm();
        if (addForm.ShowDialog() == DialogResult.OK)
        {
            LoadProducts();
        }
    }

    private void EditProduct()
    {
        if (dataGridView.SelectedRows.Count == 0)
        {
            MessageBox.Show("يرجى اختيار منتج للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        var productId = (int)dataGridView.SelectedRows[0].Cells["Id"].Value;
        var product = _context.Products.Find(productId);

        if (product != null)
        {
            var editForm = new ProductForm(product);
            if (editForm.ShowDialog() == DialogResult.OK)
            {
                LoadProducts();
            }
        }
    }

    private void DeleteProduct()
    {
        if (dataGridView.SelectedRows.Count == 0)
        {
            MessageBox.Show("يرجى اختيار منتج للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        var result = MessageBox.Show("هل أنت متأكد من حذف هذا المنتج؟", "تأكيد الحذف",
                                   MessageBoxButtons.YesNo, MessageBoxIcon.Question);

        if (result == DialogResult.Yes)
        {
            try
            {
                var productId = (int)dataGridView.SelectedRows[0].Cells["Id"].Value;
                var product = _context.Products.Find(productId);

                if (product != null)
                {
                    product.IsActive = false; // Soft delete
                    _context.SaveChanges();
                    LoadProducts();
                    MessageBox.Show("تم حذف المنتج بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المنتج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    protected override void OnFormClosed(FormClosedEventArgs e)
    {
        _context?.Dispose();
        base.OnFormClosed(e);
    }
}
