using AredooDesktopApp.Data;
using AredooDesktopApp.Models;
using Microsoft.EntityFrameworkCore;
using System.Drawing.Drawing2D;

namespace AredooDesktopApp;

public partial class Form1 : Form
{
    private AredooDbContext _context = null!;
    private User? _currentUser;

    // UI Controls
    private MenuStrip menuStrip = null!;
    private StatusStrip statusStrip = null!;
    private ToolStripStatusLabel statusLabel = null!;
    private Panel mainPanel = null!;
    private DataGridView dataGridView = null!;
    private Panel buttonPanel = null!;
    private Button addButton = null!;
    private Button editButton = null!;
    private Button deleteButton = null!;
    private Button refreshButton = null!;
    private TextBox searchTextBox = null!;
    private Label searchLabel = null!;

    public Form1()
    {
        InitializeComponent();
        InitializeDatabase();
        InitializeUI();
        LoadProducts();
    }

    private void InitializeDatabase()
    {
        _context = new AredooDbContext();
        _context.Database.EnsureCreated();

        // Login with default admin user for now
        _currentUser = _context.Users.FirstOrDefault(u => u.Username == "admin");
    }

    private void InitializeUI()
    {
        // Form settings
        this.Text = "🏪 أريدوو - نظام إدارة المبيعات والمخزون";
        this.Size = new Size(1400, 900);
        this.StartPosition = FormStartPosition.CenterScreen;
        this.WindowState = FormWindowState.Maximized;
        this.BackColor = Color.FromArgb(245, 247, 250);
        this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
        this.Icon = CreateApplicationIcon();

        // Menu Strip with modern styling
        menuStrip = new MenuStrip();
        menuStrip.BackColor = Color.FromArgb(52, 73, 94);
        menuStrip.ForeColor = Color.White;
        menuStrip.Font = new Font("Segoe UI", 10F, FontStyle.Regular);
        menuStrip.Padding = new Padding(10, 5, 10, 5);

        var fileMenu = CreateStyledMenuItem("📁 ملف", Color.FromArgb(231, 76, 60));
        var productsMenu = CreateStyledMenuItem("📦 المنتجات", Color.FromArgb(52, 152, 219));
        var categoriesMenu = CreateStyledMenuItem("🏷️ التصنيفات", Color.FromArgb(155, 89, 182));
        var reportsMenu = CreateStyledMenuItem("📊 التقارير", Color.FromArgb(230, 126, 34));
        var helpMenu = CreateStyledMenuItem("❓ مساعدة", Color.FromArgb(39, 174, 96));

        // File menu items
        fileMenu.DropDownItems.Add(CreateStyledSubMenuItem("🚪 خروج", (s, e) => {
            if (MessageBox.Show("هل تريد إغلاق التطبيق؟", "تأكيد الخروج",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                Application.Exit();
        }));

        // Products menu items
        productsMenu.DropDownItems.Add(CreateStyledSubMenuItem("👁️ عرض المنتجات", (s, e) => LoadProducts()));
        productsMenu.DropDownItems.Add(CreateStyledSubMenuItem("➕ إضافة منتج", (s, e) => AddProduct()));
        productsMenu.DropDownItems.Add(new ToolStripSeparator());
        productsMenu.DropDownItems.Add(CreateStyledSubMenuItem("📤 تصدير البيانات", (s, e) => ExportData()));
        productsMenu.DropDownItems.Add(CreateStyledSubMenuItem("📥 استيراد البيانات", (s, e) => ImportData()));

        // Categories menu items
        categoriesMenu.DropDownItems.Add(CreateStyledSubMenuItem("👁️ عرض التصنيفات", (s, e) => ShowCategories()));
        categoriesMenu.DropDownItems.Add(CreateStyledSubMenuItem("➕ إضافة تصنيف", (s, e) => AddCategory()));

        // Reports menu items
        reportsMenu.DropDownItems.Add(CreateStyledSubMenuItem("📈 تقرير المخزون", (s, e) => ShowInventoryReport()));
        reportsMenu.DropDownItems.Add(CreateStyledSubMenuItem("⚠️ المنتجات منخفضة المخزون", (s, e) => ShowLowStockReport()));

        // Help menu items
        helpMenu.DropDownItems.Add(CreateStyledSubMenuItem("📖 دليل المستخدم", (s, e) => ShowUserGuide()));
        helpMenu.DropDownItems.Add(CreateStyledSubMenuItem("ℹ️ حول البرنامج", (s, e) => ShowAbout()));

        menuStrip.Items.AddRange(new[] { fileMenu, productsMenu, categoriesMenu, reportsMenu, helpMenu });
        this.MainMenuStrip = menuStrip;
        this.Controls.Add(menuStrip);

        // Status Strip with modern styling
        statusStrip = new StatusStrip();
        statusStrip.BackColor = Color.FromArgb(52, 73, 94);
        statusStrip.ForeColor = Color.White;
        statusStrip.Font = new Font("Segoe UI", 9F);

        statusLabel = new ToolStripStatusLabel($"👤 مرحباً {_currentUser?.FullName ?? "مستخدم"}");
        statusLabel.ForeColor = Color.White;

        var timeLabel = new ToolStripStatusLabel();
        timeLabel.Text = $"🕐 {DateTime.Now:yyyy/MM/dd HH:mm}";
        timeLabel.ForeColor = Color.LightGray;
        timeLabel.Spring = true;
        timeLabel.TextAlign = ContentAlignment.MiddleRight;

        statusStrip.Items.AddRange(new ToolStripItem[] { statusLabel, timeLabel });
        this.Controls.Add(statusStrip);

        // Main Panel with gradient background
        mainPanel = new Panel
        {
            Dock = DockStyle.Fill,
            Padding = new Padding(20),
            BackColor = Color.FromArgb(245, 247, 250)
        };
        this.Controls.Add(mainPanel);

        // Header Panel
        var headerPanel = new Panel
        {
            Height = 80,
            Dock = DockStyle.Top,
            BackColor = Color.White,
            Padding = new Padding(20, 10, 20, 10)
        };

        // Add shadow effect to header
        headerPanel.Paint += (s, e) => {
            var rect = new Rectangle(0, headerPanel.Height - 2, headerPanel.Width, 2);
            using (var brush = new LinearGradientBrush(rect, Color.FromArgb(50, 0, 0, 0), Color.Transparent, LinearGradientMode.Vertical))
            {
                e.Graphics.FillRectangle(brush, rect);
            }
        };

        // Title Label
        var titleLabel = new Label
        {
            Text = "🏪 إدارة المنتجات والمخزون",
            Font = new Font("Segoe UI", 16F, FontStyle.Bold),
            ForeColor = Color.FromArgb(52, 73, 94),
            Location = new Point(20, 15),
            Size = new Size(400, 30),
            TextAlign = ContentAlignment.MiddleLeft
        };

        // Search Panel with modern styling
        var searchPanel = new Panel
        {
            Location = new Point(headerPanel.Width - 320, 20),
            Size = new Size(280, 40),
            BackColor = Color.FromArgb(236, 240, 241),
            Anchor = AnchorStyles.Top | AnchorStyles.Right
        };

        // Round corners for search panel
        searchPanel.Paint += (s, e) => {
            var rect = new Rectangle(0, 0, searchPanel.Width, searchPanel.Height);
            using (var path = CreateRoundedRectanglePath(rect, 20))
            {
                e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
                e.Graphics.FillPath(new SolidBrush(Color.FromArgb(236, 240, 241)), path);
                e.Graphics.DrawPath(new Pen(Color.FromArgb(189, 195, 199), 1), path);
            }
        };

        searchLabel = new Label
        {
            Text = "🔍",
            Location = new Point(10, 12),
            Size = new Size(20, 16),
            Font = new Font("Segoe UI", 10F),
            ForeColor = Color.FromArgb(127, 140, 141),
            BackColor = Color.Transparent
        };

        searchTextBox = new TextBox
        {
            Location = new Point(35, 10),
            Size = new Size(235, 20),
            BorderStyle = BorderStyle.None,
            Font = new Font("Segoe UI", 10F),
            BackColor = Color.FromArgb(236, 240, 241),
            ForeColor = Color.FromArgb(52, 73, 94)
        };
        searchTextBox.TextChanged += SearchTextBox_TextChanged;
        searchTextBox.Enter += (s, e) => searchPanel.BackColor = Color.FromArgb(220, 230, 241);
        searchTextBox.Leave += (s, e) => searchPanel.BackColor = Color.FromArgb(236, 240, 241);

        // Add placeholder text
        searchTextBox.Text = "ابحث عن منتج...";
        searchTextBox.ForeColor = Color.Gray;
        searchTextBox.Enter += (s, e) => {
            if (searchTextBox.Text == "ابحث عن منتج...")
            {
                searchTextBox.Text = "";
                searchTextBox.ForeColor = Color.FromArgb(52, 73, 94);
            }
        };
        searchTextBox.Leave += (s, e) => {
            if (string.IsNullOrWhiteSpace(searchTextBox.Text))
            {
                searchTextBox.Text = "ابحث عن منتج...";
                searchTextBox.ForeColor = Color.Gray;
            }
        };

        searchPanel.Controls.AddRange(new Control[] { searchLabel, searchTextBox });
        headerPanel.Controls.AddRange(new Control[] { titleLabel, searchPanel });
        mainPanel.Controls.Add(headerPanel);

        // Button Panel with modern styling
        buttonPanel = new Panel
        {
            Height = 70,
            Dock = DockStyle.Top,
            BackColor = Color.White,
            Padding = new Padding(20, 15, 20, 15)
        };

        // Add shadow effect to button panel
        buttonPanel.Paint += (s, e) => {
            var rect = new Rectangle(0, buttonPanel.Height - 2, buttonPanel.Width, 2);
            using (var brush = new LinearGradientBrush(rect, Color.FromArgb(30, 0, 0, 0), Color.Transparent, LinearGradientMode.Vertical))
            {
                e.Graphics.FillRectangle(brush, rect);
            }
        };

        // Create modern styled buttons
        addButton = CreateModernButton("➕ إضافة منتج", Color.FromArgb(46, 204, 113), Color.FromArgb(39, 174, 96));
        addButton.Location = new Point(20, 15);
        addButton.Click += (s, e) => AddProduct();

        editButton = CreateModernButton("✏️ تعديل", Color.FromArgb(52, 152, 219), Color.FromArgb(41, 128, 185));
        editButton.Location = new Point(160, 15);
        editButton.Click += (s, e) => EditProduct();

        deleteButton = CreateModernButton("🗑️ حذف", Color.FromArgb(231, 76, 60), Color.FromArgb(192, 57, 43));
        deleteButton.Location = new Point(270, 15);
        deleteButton.Click += (s, e) => DeleteProduct();

        refreshButton = CreateModernButton("🔄 تحديث", Color.FromArgb(155, 89, 182), Color.FromArgb(142, 68, 173));
        refreshButton.Location = new Point(380, 15);
        refreshButton.Click += (s, e) => LoadProducts();

        // Statistics Panel
        var statsPanel = new Panel
        {
            Location = new Point(buttonPanel.Width - 300, 10),
            Size = new Size(280, 50),
            BackColor = Color.Transparent,
            Anchor = AnchorStyles.Top | AnchorStyles.Right
        };

        var totalProductsLabel = new Label
        {
            Text = "📦 إجمالي المنتجات: 0",
            Location = new Point(0, 5),
            Size = new Size(140, 20),
            Font = new Font("Segoe UI", 9F, FontStyle.Bold),
            ForeColor = Color.FromArgb(52, 73, 94),
            TextAlign = ContentAlignment.MiddleCenter,
            BackColor = Color.FromArgb(236, 240, 241)
        };

        var lowStockLabel = new Label
        {
            Text = "⚠️ مخزون منخفض: 0",
            Location = new Point(145, 5),
            Size = new Size(130, 20),
            Font = new Font("Segoe UI", 9F, FontStyle.Bold),
            ForeColor = Color.FromArgb(231, 76, 60),
            TextAlign = ContentAlignment.MiddleCenter,
            BackColor = Color.FromArgb(255, 235, 235)
        };

        // Round corners for stat labels
        totalProductsLabel.Paint += (s, e) => DrawRoundedLabel(e, totalProductsLabel, Color.FromArgb(236, 240, 241));
        lowStockLabel.Paint += (s, e) => DrawRoundedLabel(e, lowStockLabel, Color.FromArgb(255, 235, 235));

        statsPanel.Controls.AddRange(new Control[] { totalProductsLabel, lowStockLabel });

        buttonPanel.Controls.AddRange(new Control[] { addButton, editButton, deleteButton, refreshButton, statsPanel });
        mainPanel.Controls.Add(buttonPanel);

        // DataGridView Container with modern styling
        var gridContainer = new Panel
        {
            Dock = DockStyle.Fill,
            Padding = new Padding(0, 10, 0, 0),
            BackColor = Color.Transparent
        };

        dataGridView = new DataGridView
        {
            Dock = DockStyle.Fill,
            AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            MultiSelect = false,
            ReadOnly = true,
            AllowUserToAddRows = false,
            AllowUserToDeleteRows = false,
            RightToLeft = RightToLeft.Yes,

            // Modern styling
            BackgroundColor = Color.White,
            BorderStyle = BorderStyle.None,
            CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
            ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
            RowHeadersVisible = false,
            EnableHeadersVisualStyles = false,
            AllowUserToResizeRows = false,
            RowTemplate = { Height = 35 },

            // Header styling
            ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
            {
                BackColor = Color.FromArgb(52, 73, 94),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Alignment = DataGridViewContentAlignment.MiddleCenter,
                SelectionBackColor = Color.FromArgb(52, 73, 94),
                SelectionForeColor = Color.White,
                Padding = new Padding(5)
            },

            // Cell styling
            DefaultCellStyle = new DataGridViewCellStyle
            {
                BackColor = Color.White,
                ForeColor = Color.FromArgb(52, 73, 94),
                Font = new Font("Segoe UI", 9F),
                SelectionBackColor = Color.FromArgb(52, 152, 219),
                SelectionForeColor = Color.White,
                Alignment = DataGridViewContentAlignment.MiddleCenter,
                Padding = new Padding(5)
            },

            // Alternating row colors
            AlternatingRowsDefaultCellStyle = new DataGridViewCellStyle
            {
                BackColor = Color.FromArgb(248, 249, 250),
                ForeColor = Color.FromArgb(52, 73, 94),
                Font = new Font("Segoe UI", 9F),
                SelectionBackColor = Color.FromArgb(52, 152, 219),
                SelectionForeColor = Color.White,
                Alignment = DataGridViewContentAlignment.MiddleCenter,
                Padding = new Padding(5)
            }
        };

        // Add double-click event for editing
        dataGridView.CellDoubleClick += (s, e) => {
            if (e.RowIndex >= 0)
            {
                EditProduct();
            }
        };

        // Add hover effect
        dataGridView.CellMouseEnter += (s, e) => {
            if (e.RowIndex >= 0)
            {
                dataGridView.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.FromArgb(230, 240, 250);
            }
        };

        dataGridView.CellMouseLeave += (s, e) => {
            if (e.RowIndex >= 0)
            {
                // Reset to original color based on low stock status
                var isLowStock = dataGridView.Rows[e.RowIndex].Cells["حالة_المخزون"]?.Value?.ToString() == "منخفض";
                if (isLowStock)
                {
                    dataGridView.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.FromArgb(255, 235, 235);
                }
                else
                {
                    dataGridView.Rows[e.RowIndex].DefaultCellStyle.BackColor =
                        e.RowIndex % 2 == 0 ? Color.White : Color.FromArgb(248, 249, 250);
                }
            }
        };

        gridContainer.Controls.Add(dataGridView);
        mainPanel.Controls.Add(gridContainer);
    }

    private void LoadProducts()
    {
        try
        {
            var products = _context.Products
                .Where(p => p.IsActive)
                .OrderBy(p => p.Name)
                .Select(p => new
                {
                    p.Id,
                    الكود = p.Code,
                    الاسم = p.DisplayName,
                    التصنيف = p.Category,
                    الوحدة = p.Unit,
                    سعر_الشراء = p.PurchasePrice,
                    سعر_البيع = p.SalePrice,
                    الكمية = p.Quantity,
                    الحد_الأدنى = p.MinQuantity,
                    حالة_المخزون = p.IsLowStock ? "منخفض" : "طبيعي"
                })
                .ToList();

            dataGridView.DataSource = products;

            // Hide ID column
            if (dataGridView.Columns["Id"] != null)
                dataGridView.Columns["Id"].Visible = false;

            // Color low stock rows
            foreach (DataGridViewRow row in dataGridView.Rows)
            {
                if (row.Cells["حالة_المخزون"].Value?.ToString() == "منخفض")
                {
                    row.DefaultCellStyle.BackColor = Color.LightPink;
                }
            }

            statusLabel.Text = $"تم تحميل {products.Count} منتج";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void SearchTextBox_TextChanged(object? sender, EventArgs e)
    {
        if (string.IsNullOrWhiteSpace(searchTextBox.Text))
        {
            LoadProducts();
            return;
        }

        try
        {
            var searchTerm = searchTextBox.Text.ToLower();
            var products = _context.Products
                .Where(p => p.IsActive &&
                           (p.Name.ToLower().Contains(searchTerm) ||
                            p.NameAr.ToLower().Contains(searchTerm) ||
                            p.Code.ToLower().Contains(searchTerm) ||
                            p.Category.ToLower().Contains(searchTerm)))
                .OrderBy(p => p.Name)
                .Select(p => new
                {
                    p.Id,
                    الكود = p.Code,
                    الاسم = p.DisplayName,
                    التصنيف = p.Category,
                    الوحدة = p.Unit,
                    سعر_الشراء = p.PurchasePrice,
                    سعر_البيع = p.SalePrice,
                    الكمية = p.Quantity,
                    الحد_الأدنى = p.MinQuantity,
                    حالة_المخزون = p.IsLowStock ? "منخفض" : "طبيعي"
                })
                .ToList();

            dataGridView.DataSource = products;

            if (dataGridView.Columns["Id"] != null)
                dataGridView.Columns["Id"].Visible = false;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void AddProduct()
    {
        var addForm = new ProductForm();
        if (addForm.ShowDialog() == DialogResult.OK)
        {
            LoadProducts();
        }
    }

    private void EditProduct()
    {
        if (dataGridView.SelectedRows.Count == 0)
        {
            MessageBox.Show("يرجى اختيار منتج للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        var productId = (int)dataGridView.SelectedRows[0].Cells["Id"].Value;
        var product = _context.Products.Find(productId);

        if (product != null)
        {
            var editForm = new ProductForm(product);
            if (editForm.ShowDialog() == DialogResult.OK)
            {
                LoadProducts();
            }
        }
    }

    private void DeleteProduct()
    {
        if (dataGridView.SelectedRows.Count == 0)
        {
            MessageBox.Show("يرجى اختيار منتج للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        var result = MessageBox.Show("هل أنت متأكد من حذف هذا المنتج؟", "تأكيد الحذف",
                                   MessageBoxButtons.YesNo, MessageBoxIcon.Question);

        if (result == DialogResult.Yes)
        {
            try
            {
                var productId = (int)dataGridView.SelectedRows[0].Cells["Id"].Value;
                var product = _context.Products.Find(productId);

                if (product != null)
                {
                    product.IsActive = false; // Soft delete
                    _context.SaveChanges();
                    LoadProducts();
                    MessageBox.Show("تم حذف المنتج بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المنتج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    protected override void OnFormClosed(FormClosedEventArgs e)
    {
        _context?.Dispose();
        base.OnFormClosed(e);
    }

    // Helper methods for modern UI styling
    private Icon CreateApplicationIcon()
    {
        // Create a simple icon programmatically
        var bitmap = new Bitmap(32, 32);
        using (var g = Graphics.FromImage(bitmap))
        {
            g.Clear(Color.FromArgb(52, 152, 219));
            g.FillEllipse(new SolidBrush(Color.White), 8, 8, 16, 16);
            g.DrawString("A", new Font("Arial", 12, FontStyle.Bold),
                        new SolidBrush(Color.FromArgb(52, 152, 219)), 11, 9);
        }
        return Icon.FromHandle(bitmap.GetHicon());
    }

    private ToolStripMenuItem CreateStyledMenuItem(string text, Color accentColor)
    {
        var item = new ToolStripMenuItem(text);
        item.ForeColor = Color.White;
        item.Font = new Font("Segoe UI", 10F, FontStyle.Regular);
        item.Padding = new Padding(10, 5, 10, 5);

        // Add hover effect
        item.MouseEnter += (s, e) => {
            item.BackColor = accentColor;
        };
        item.MouseLeave += (s, e) => {
            item.BackColor = Color.Transparent;
        };

        return item;
    }

    private ToolStripMenuItem CreateStyledSubMenuItem(string text, EventHandler clickHandler)
    {
        var item = new ToolStripMenuItem(text);
        item.ForeColor = Color.Black;
        item.Font = new Font("Segoe UI", 9F);
        item.Click += clickHandler;

        // Add hover effect
        item.MouseEnter += (s, e) => {
            item.BackColor = Color.FromArgb(52, 152, 219);
            item.ForeColor = Color.White;
        };
        item.MouseLeave += (s, e) => {
            item.BackColor = Color.Transparent;
            item.ForeColor = Color.Black;
        };

        return item;
    }

    private Button CreateModernButton(string text, Color normalColor, Color hoverColor)
    {
        var button = new Button
        {
            Text = text,
            Size = new Size(120, 40),
            FlatStyle = FlatStyle.Flat,
            BackColor = normalColor,
            ForeColor = Color.White,
            Font = new Font("Segoe UI", 9F, FontStyle.Bold),
            Cursor = Cursors.Hand,
            TextAlign = ContentAlignment.MiddleCenter
        };

        button.FlatAppearance.BorderSize = 0;
        button.FlatAppearance.MouseOverBackColor = hoverColor;
        button.FlatAppearance.MouseDownBackColor = Color.FromArgb(Math.Max(0, hoverColor.R - 20),
                                                                  Math.Max(0, hoverColor.G - 20),
                                                                  Math.Max(0, hoverColor.B - 20));

        // Add rounded corners
        button.Paint += (s, e) => {
            var rect = new Rectangle(0, 0, button.Width, button.Height);
            using (var path = CreateRoundedRectanglePath(rect, 8))
            {
                e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
                e.Graphics.FillPath(new SolidBrush(button.BackColor), path);

                // Add subtle shadow
                var shadowRect = new Rectangle(2, 2, button.Width, button.Height);
                using (var shadowPath = CreateRoundedRectanglePath(shadowRect, 8))
                {
                    e.Graphics.FillPath(new SolidBrush(Color.FromArgb(20, 0, 0, 0)), shadowPath);
                }

                e.Graphics.FillPath(new SolidBrush(button.BackColor), path);
            }
        };

        return button;
    }

    private GraphicsPath CreateRoundedRectanglePath(Rectangle rect, int cornerRadius)
    {
        var path = new GraphicsPath();
        var diameter = cornerRadius * 2;

        path.AddArc(rect.X, rect.Y, diameter, diameter, 180, 90);
        path.AddArc(rect.Right - diameter, rect.Y, diameter, diameter, 270, 90);
        path.AddArc(rect.Right - diameter, rect.Bottom - diameter, diameter, diameter, 0, 90);
        path.AddArc(rect.X, rect.Bottom - diameter, diameter, diameter, 90, 90);
        path.CloseFigure();

        return path;
    }

    private void DrawRoundedLabel(PaintEventArgs e, Label label, Color backgroundColor)
    {
        var rect = new Rectangle(0, 0, label.Width, label.Height);
        using (var path = CreateRoundedRectanglePath(rect, 10))
        {
            e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
            e.Graphics.FillPath(new SolidBrush(backgroundColor), path);
        }
    }

    // Placeholder methods for menu actions
    private void ExportData() => MessageBox.Show("ميزة تصدير البيانات قيد التطوير", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
    private void ImportData() => MessageBox.Show("ميزة استيراد البيانات قيد التطوير", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
    private void ShowCategories() => MessageBox.Show("ميزة إدارة التصنيفات قيد التطوير", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
    private void AddCategory() => MessageBox.Show("ميزة إضافة التصنيفات قيد التطوير", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
    private void ShowInventoryReport() => MessageBox.Show("ميزة تقارير المخزون قيد التطوير", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
    private void ShowLowStockReport() => ShowLowStockProducts();
    private void ShowUserGuide() => MessageBox.Show("دليل المستخدم:\n\n1. استخدم قائمة المنتجات لإدارة المنتجات\n2. استخدم البحث للعثور على منتجات محددة\n3. انقر مرتين على منتج لتعديله\n4. المنتجات منخفضة المخزون تظهر باللون الوردي", "دليل المستخدم", MessageBoxButtons.OK, MessageBoxIcon.Information);
    private void ShowAbout() => MessageBox.Show("أريدوو - نظام إدارة المبيعات والمخزون\nالإصدار 1.0.0\n\nتم التطوير بواسطة: Augment Agent\nالتقنية: C# + Windows Forms + .NET 9\nقاعدة البيانات: SQLite\n\n© 2025 جميع الحقوق محفوظة", "حول البرنامج", MessageBoxButtons.OK, MessageBoxIcon.Information);

    private void ShowLowStockProducts()
    {
        try
        {
            var lowStockProducts = _context.Products
                .Where(p => p.IsActive && p.Quantity <= p.MinQuantity)
                .OrderBy(p => p.Quantity)
                .Select(p => new
                {
                    p.Id,
                    الكود = p.Code,
                    الاسم = p.DisplayName,
                    الكمية_الحالية = p.Quantity,
                    الحد_الأدنى = p.MinQuantity,
                    النقص = p.MinQuantity - p.Quantity
                })
                .ToList();

            if (lowStockProducts.Any())
            {
                dataGridView.DataSource = lowStockProducts;
                if (dataGridView.Columns["Id"] != null)
                    dataGridView.Columns["Id"].Visible = false;

                foreach (DataGridViewRow row in dataGridView.Rows)
                {
                    row.DefaultCellStyle.BackColor = Color.LightPink;
                }

                statusLabel.Text = $"⚠️ تم العثور على {lowStockProducts.Count} منتج منخفض المخزون";
            }
            else
            {
                MessageBox.Show("✅ جميع المنتجات لديها مخزون كافي", "تقرير المخزون", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في عرض تقرير المخزون: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
}
