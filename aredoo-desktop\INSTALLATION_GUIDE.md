# 📥 دليل التثبيت والتشغيل
## Installation and Setup Guide

### 🎯 متطلبات النظام | System Requirements

#### الحد الأدنى | Minimum Requirements:
- **نظام التشغيل**: Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **المعالج**: Intel Core i3 أو AMD Ryzen 3
- **الذاكرة**: 4GB RAM
- **مساحة القرص**: 1GB مساحة فارغة
- **الشاشة**: دقة 1024x768 أو أعلى

#### الموصى به | Recommended:
- **المعالج**: Intel Core i5 أو AMD Ryzen 5
- **الذاكرة**: 8GB RAM
- **مساحة القرص**: 2GB مساحة فارغة
- **الشاشة**: دقة 1920x1080 أو أعلى

---

### ☕ تثبيت Java | Java Installation

#### الخطوة 1: تحميل Java
قم بتحميل Java 11 أو أحدث من أحد المواقع التالية:

**الخيار الأول - Eclipse Temurin (مجاني ومفتوح المصدر):**
1. اذهب إلى: https://adoptium.net/
2. اختر **Java 11 (LTS)** أو **Java 17 (LTS)**
3. اختر نظام التشغيل الخاص بك
4. حمل الملف وثبته

**الخيار الثاني - Oracle JDK:**
1. اذهب إلى: https://www.oracle.com/java/technologies/downloads/
2. اختر **Java 11** أو أحدث
3. حمل الملف وثبته

#### الخطوة 2: التحقق من التثبيت
افتح موجه الأوامر (Command Prompt) واكتب:
```bash
java -version
```

يجب أن تظهر رسالة مشابهة لهذه:
```
java version "11.0.x" 2023-xx-xx LTS
Java(TM) SE Runtime Environment (build 11.0.x+xx-LTS)
Java HotSpot(TM) 64-Bit Server VM (build 11.0.x+xx-LTS, mixed mode)
```

---

### 🚀 تشغيل التطبيق | Running the Application

#### الطريقة الأولى - ملف التشغيل التلقائي (الأسهل):
1. انقر مرتين على `start-aredoo-desktop.bat`
2. سيتم فحص النظام تلقائياً وتشغيل التطبيق

#### الطريقة الثانية - ملف التشغيل البسيط:
1. انقر مرتين على `run-desktop.bat`
2. أو افتح موجه الأوامر في مجلد التطبيق واكتب:
```bash
run-desktop.bat
```

#### الطريقة الثالثة - Maven (للمطورين):
```bash
# بناء المشروع
mvn clean compile

# تشغيل التطبيق
mvn javafx:run
```

---

### 🔧 حل المشاكل الشائعة | Troubleshooting

#### المشكلة: "java is not recognized"
**السبب**: Java غير مثبت أو غير موجود في PATH

**الحل**:
1. تأكد من تثبيت Java كما هو موضح أعلاه
2. أعد تشغيل الكمبيوتر بعد التثبيت
3. تحقق من متغير البيئة JAVA_HOME:
   - افتح "System Properties" → "Environment Variables"
   - تأكد من وجود JAVA_HOME يشير إلى مجلد Java
   - تأكد من وجود %JAVA_HOME%\bin في PATH

#### المشكلة: "JavaFX runtime components are missing"
**السبب**: JavaFX غير متوفر

**الحل**:
1. استخدم Java 11 مع JavaFX مدمج
2. أو حمل JavaFX منفصلاً من: https://openjfx.io/
3. أو استخدم Eclipse Temurin الذي يتضمن JavaFX

#### المشكلة: التطبيق لا يبدأ
**الحل**:
1. تحقق من ملف السجل في مجلد `logs/`
2. تأكد من وجود مجلد `data/` 
3. تحقق من الصلاحيات (يجب أن يكون للتطبيق صلاحية الكتابة)
4. جرب تشغيل التطبيق كمدير (Run as Administrator)

#### المشكلة: النص العربي لا يظهر بشكل صحيح
**الحل**:
1. تأكد من أن نظام التشغيل يدعم اللغة العربية
2. ثبت خطوط عربية إضافية إذا لزم الأمر
3. تأكد من إعدادات اللغة في Windows

---

### 👤 بيانات الدخول الافتراضية | Default Login

عند تشغيل التطبيق لأول مرة، استخدم:

**المدير | Administrator:**
- اسم المستخدم: `admin`
- كلمة المرور: `1234`

**الكاشير | Cashier:**
- اسم المستخدم: `cashier`
- كلمة المرور: `1234`

⚠️ **مهم**: قم بتغيير كلمات المرور الافتراضية فور تسجيل الدخول!

---

### 📁 ملفات التطبيق | Application Files

#### الملفات المهمة:
- `data/` - قاعدة البيانات المحلية
- `logs/` - ملفات السجل
- `backups/` - النسخ الاحتياطية (إن وجدت)
- `src/main/resources/application.properties` - إعدادات التطبيق

#### النسخ الاحتياطي:
- قم بنسخ مجلد `data/` بانتظام
- احتفظ بنسخة من ملف `application.properties` إذا عدلته

---

### 🆘 الدعم الفني | Technical Support

إذا واجهت أي مشاكل:

1. **تحقق من ملف السجل**: `logs/aredoo-desktop.log`
2. **راجع هذا الدليل** للحلول الشائعة
3. **تواصل معنا**:
   - البريد الإلكتروني: <EMAIL>
   - الموقع: www.aredoo.com

عند التواصل، يرجى تضمين:
- نظام التشغيل وإصداره
- إصدار Java المثبت
- رسالة الخطأ كاملة
- محتوى ملف السجل

---

### 🔄 التحديثات | Updates

للحصول على آخر التحديثات:
1. زر الموقع الرسمي: www.aredoo.com
2. حمل الإصدار الجديد
3. انسخ مجلد `data/` من الإصدار القديم إلى الجديد
4. شغل التطبيق الجديد

---

**نتمنى لك تجربة ممتعة مع أريدوو! 🎉**  
**Enjoy using Aredoo! 🎉**
