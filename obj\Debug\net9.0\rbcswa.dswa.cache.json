{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["SZNKdGCTpwiYvfE+bdvgc+8O0UvouQKr0i1beyn7H6Y=", "7D7nuOLQ/L9R2gQCQLDzLVJKLvOop2gdKaIWaPJSvFE=", "+/+cqqXze6iXC/OYdwTLLoYxEpLBtAXPOSffggMQ348=", "nRABpE1FSRlIk2eBsz+75HtLctZhLGfFM/lIuex6KEE=", "4whRnnDODgppMsUUFMA++FIVPtAM57cTYgyFEJxv8aQ=", "pRylSjaSpwx9QKjWKXIHO/1gMe4vokKV656BoyZBCF8=", "rdk3gTM5svtB3aF9O7DXmw3h58ywwYkGramgLXgChnc=", "XsRWPwXQdaaz4tVEwnqzENjpl8MUV5VJN0BCOsWJPP0=", "01toL9yPNtsUTvbqEaMHiQOdAVzygJa6TfeGn/UhP5I=", "iwQtGvtBWinmaov5ts/VQsdpJhOmqswICXgB3IMGPIs=", "sob8XGd+ux2yjPK99QRY4qbCemQrHm4NbYtPgKsQUHU=", "wnmAXclGD0PWNbORXL0+HcFpN3RbU70b1OW/O8vEXY0=", "y+CPZP38elUqUrdegILukcaQWEuAY6zIPhuYzcnnoBs=", "JFA5UZOL0d/izumazVYG91MzC6zr3ZlHiZuFB3IeJ34=", "FnjiN1uBfSoXVwpjQoD3U5MN5z0cgLOSPm6tdfR9TIk=", "ZoJYWY9H58kLzZGblpehGgO12QJokylvo3fx7Wmt5Kg="], "CachedAssets": {"SZNKdGCTpwiYvfE+bdvgc+8O0UvouQKr0i1beyn7H6Y=": {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\tghp5tkl3n-zdfn4todj4.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "css/style#[.{fingerprint=zdfn4todj4}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\css\\style.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "79geveex89", "Integrity": "y+C+NGyTs1Bmv9JKA0wuI+utf8buSUwkKfizsp7zRkg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\css\\style.css", "FileLength": 7604, "LastWriteTime": "2025-11-20T14:43:22.9558827+00:00"}, "7D7nuOLQ/L9R2gQCQLDzLVJKLvOop2gdKaIWaPJSvFE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\25wrlhmap0-j1wgh6mdca.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "index#[.{fingerprint=j1wgh6mdca}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fl8j0yecj5", "Integrity": "Q1rMoUlGhutqC43l/Swj21FGxwOnw1gAhxocbbGJodA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\index.html", "FileLength": 1421, "LastWriteTime": "2025-11-20T14:43:22.9558827+00:00"}, "+/+cqqXze6iXC/OYdwTLLoYxEpLBtAXPOSffggMQ348=": {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\wg78tukmqu-1posqj9azf.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/app#[.{fingerprint=1posqj9azf}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "horooyit8m", "Integrity": "IeIe2aS54ONtnLY1VQrOEwcC51f9qysdgbQPQ55G4Mc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\app.js", "FileLength": 2100, "LastWriteTime": "2025-11-20T14:43:22.9452058+00:00"}, "nRABpE1FSRlIk2eBsz+75HtLctZhLGfFM/lIuex6KEE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\8c7jydm8ty-iyy8yegl4k.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/auth#[.{fingerprint=iyy8yegl4k}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\auth.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "voushzzqiw", "Integrity": "X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\auth.js", "FileLength": 1135, "LastWriteTime": "2025-11-20T14:43:22.9578925+00:00"}, "4whRnnDODgppMsUUFMA++FIVPtAM57cTYgyFEJxv8aQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\vkpingxf2f-q4k5ude2ax.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/categories#[.{fingerprint=q4k5ude2ax}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\categories.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3h5qqccxjn", "Integrity": "N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\categories.js", "FileLength": 2390, "LastWriteTime": "2025-11-20T14:43:22.9452058+00:00"}, "pRylSjaSpwx9QKjWKXIHO/1gMe4vokKV656BoyZBCF8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\ill7x5ozqf-hy3h9tabsi.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/customers#[.{fingerprint=hy3h9tabsi}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\customers.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nynguhmsve", "Integrity": "sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\customers.js", "FileLength": 2684, "LastWriteTime": "2025-11-20T14:43:22.9558827+00:00"}, "rdk3gTM5svtB3aF9O7DXmw3h58ywwYkGramgLXgChnc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\x62rvtqy4o-sw1pmnb1tp.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/expenses#[.{fingerprint=sw1pmnb1tp}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\expenses.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x7x4kbs8j0", "Integrity": "0x7sxXZp5/3Pq8eQxZvOOUu4iOcv28hKJy22PKDxe8A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\expenses.js", "FileLength": 3438, "LastWriteTime": "2025-11-20T14:43:22.9452058+00:00"}, "XsRWPwXQdaaz4tVEwnqzENjpl8MUV5VJN0BCOsWJPP0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\sif3j9439v-7ene3dnx54.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/invoices#[.{fingerprint=7ene3dnx54}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\invoices.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w8qy1uzoa1", "Integrity": "oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\invoices.js", "FileLength": 4915, "LastWriteTime": "2025-11-20T14:43:22.9558827+00:00"}, "01toL9yPNtsUTvbqEaMHiQOdAVzygJa6TfeGn/UhP5I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\5lixpq0z40-pjnfkijmjf.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/pos#[.{fingerprint=pjnfkijmjf}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\pos.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xrj6uyu0ck", "Integrity": "Y9AAQFdpxbnxeOLCYmeDn/xDQvyT15WTuIgo2q+uaA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\pos.js", "FileLength": 7718, "LastWriteTime": "2025-11-20T14:43:22.9452058+00:00"}, "iwQtGvtBWinmaov5ts/VQsdpJhOmqswICXgB3IMGPIs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\wa9tzd9v0l-9n0jr0b026.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/products#[.{fingerprint=9n0jr0b026}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\products.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ou6rhzkhk7", "Integrity": "cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\products.js", "FileLength": 8284, "LastWriteTime": "2025-11-20T14:43:22.9578925+00:00"}, "sob8XGd+ux2yjPK99QRY4qbCemQrHm4NbYtPgKsQUHU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\j099mgtiqd-uxkkg3wsx6.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/purchases#[.{fingerprint=uxkkg3wsx6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\purchases.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nant1d1596", "Integrity": "L2DAn2hNrvcZKW4yRq71tVHjOcqPKCI67MD5j5IHvmI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\purchases.js", "FileLength": 3854, "LastWriteTime": "2025-11-20T14:43:22.9452058+00:00"}, "wnmAXclGD0PWNbORXL0+HcFpN3RbU70b1OW/O8vEXY0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\x78a6k87cc-2zj8ofrn42.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/reports#[.{fingerprint=2zj8ofrn42}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\reports.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kvzwheefbe", "Integrity": "oMYbV5AVYI0xejYZoQHZnI3REekGeYg+bDNc3rpK6pk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\reports.js", "FileLength": 6774, "LastWriteTime": "2025-11-20T14:43:22.9578925+00:00"}, "y+CPZP38elUqUrdegILukcaQWEuAY6zIPhuYzcnnoBs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\kd4at48or8-kgsze9z630.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/settings#[.{fingerprint=kgsze9z630}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\settings.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9gj<PERSON><PERSON><PERSON>", "Integrity": "uc88cmSCVyx4BQE3QaHAfuGDykSaV72SH+Z0KBHEbvg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\settings.js", "FileLength": 13145, "LastWriteTime": "2025-11-20T14:43:22.9558827+00:00"}, "JFA5UZOL0d/izumazVYG91MzC6zr3ZlHiZuFB3IeJ34=": {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\eoulc2yy7k-sb2z2q5wz2.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/suppliers#[.{fingerprint=sb2z2q5wz2}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\suppliers.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "79pdnt8t6b", "Integrity": "G4zjpvKHBa9FT9vBtheUVUNyGnh082JCUyN20sqgGgE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\suppliers.js", "FileLength": 2405, "LastWriteTime": "2025-11-20T14:43:22.9578925+00:00"}, "FnjiN1uBfSoXVwpjQoD3U5MN5z0cgLOSPm6tdfR9TIk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\ziqm8tcidl-nqonojhrly.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "login#[.{fingerprint=nqonojhrly}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\login.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dp8os1n73s", "Integrity": "701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\login.html", "FileLength": 2051, "LastWriteTime": "2025-11-20T14:43:22.9558827+00:00"}, "ZoJYWY9H58kLzZGblpehGgO12QJokylvo3fx7Wmt5Kg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\pv27i78e2o-2cv1skr50a.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "print-invoice#[.{fingerprint=2cv1skr50a}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\print-invoice.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f16cn8vlan", "Integrity": "bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\print-invoice.html", "FileLength": 3987, "LastWriteTime": "2025-11-20T14:43:22.9578925+00:00"}}, "CachedCopyCandidates": {}}