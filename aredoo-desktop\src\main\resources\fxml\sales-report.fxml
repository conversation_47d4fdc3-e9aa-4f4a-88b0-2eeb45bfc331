<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" 
      fx:controller="com.aredoo.desktop.controller.SalesReportController"
      spacing="10" styleClass="form-container">

   <padding>
      <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
   </padding>

   <!-- Header -->
   <HBox alignment="CENTER_LEFT" spacing="10">
      <Label text="تقرير المبيعات" styleClass="section-title">
         <font>
            <Font name="Arial" size="18.0" />
         </font>
      </Label>
      
      <Region HBox.hgrow="ALWAYS"/>
      
      <Button text="تصدير PDF" onAction="#exportToPDF" 
              styleClass="toolbar-button" prefWidth="100"/>
      <Button text="طباعة" onAction="#printReport" 
              styleClass="button" prefWidth="80"/>
   </HBox>

   <!-- Date Range Selection -->
   <HBox spacing="15" alignment="CENTER_LEFT" styleClass="toolbar">
      <Label text="الفترة الزمنية:">
         <font>
            <Font name="Arial" size="12.0" />
         </font>
      </Label>
      
      <Label text="من:"/>
      <DatePicker fx:id="fromDatePicker" prefWidth="120"/>
      
      <Label text="إلى:"/>
      <DatePicker fx:id="toDatePicker" prefWidth="120"/>
      
      <Button text="عرض التقرير" onAction="#generateReport" styleClass="quick-action-button"/>
      
      <Separator orientation="VERTICAL"/>
      
      <Button text="اليوم" onAction="#setToday" styleClass="button"/>
      <Button text="هذا الأسبوع" onAction="#setThisWeek" styleClass="button"/>
      <Button text="هذا الشهر" onAction="#setThisMonth" styleClass="button"/>
   </HBox>

   <!-- Summary Cards -->
   <GridPane hgap="15" vgap="15" styleClass="stats-grid">
      <VBox styleClass="stat-card" GridPane.columnIndex="0" GridPane.rowIndex="0">
         <Label text="إجمالي المبيعات" styleClass="stat-title"/>
         <Label fx:id="totalSalesLabel" text="0.00 د.ع" styleClass="stat-value"/>
         <Label text="للفترة المحددة" styleClass="stat-subtitle"/>
      </VBox>
      
      <VBox styleClass="stat-card" GridPane.columnIndex="1" GridPane.rowIndex="0">
         <Label text="عدد الفواتير" styleClass="stat-title"/>
         <Label fx:id="totalInvoicesLabel" text="0" styleClass="stat-value"/>
         <Label text="فاتورة" styleClass="stat-subtitle"/>
      </VBox>
      
      <VBox styleClass="stat-card" GridPane.columnIndex="2" GridPane.rowIndex="0">
         <Label text="متوسط الفاتورة" styleClass="stat-title"/>
         <Label fx:id="averageInvoiceLabel" text="0.00 د.ع" styleClass="stat-value"/>
         <Label text="متوسط" styleClass="stat-subtitle"/>
      </VBox>
      
      <VBox styleClass="stat-card" GridPane.columnIndex="0" GridPane.rowIndex="1">
         <Label text="المبلغ المدفوع" styleClass="stat-title"/>
         <Label fx:id="totalPaidLabel" text="0.00 د.ع" styleClass="stat-value"/>
         <Label text="مدفوع" styleClass="stat-subtitle"/>
      </VBox>
      
      <VBox styleClass="stat-card alert-card" GridPane.columnIndex="1" GridPane.rowIndex="1">
         <Label text="المبلغ المتبقي" styleClass="stat-title"/>
         <Label fx:id="totalRemainingLabel" text="0.00 د.ع" styleClass="stat-value"/>
         <Label text="متبقي" styleClass="stat-subtitle"/>
      </VBox>
      
      <VBox styleClass="stat-card" GridPane.columnIndex="2" GridPane.rowIndex="1">
         <Label text="صافي الربح" styleClass="stat-title"/>
         <Label fx:id="netProfitLabel" text="0.00 د.ع" styleClass="stat-value"/>
         <Label text="ربح" styleClass="stat-subtitle"/>
      </VBox>
   </GridPane>

   <!-- Charts and Details -->
   <TabPane VBox.vgrow="ALWAYS">
      <!-- Sales by Date Tab -->
      <Tab text="المبيعات حسب التاريخ" closable="false">
         <VBox spacing="10">
            <padding>
               <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
            </padding>
            
            <!-- Chart placeholder -->
            <Label text="رسم بياني للمبيعات حسب التاريخ" styleClass="section-title" 
                   style="-fx-alignment: center; -fx-min-height: 200px; -fx-background-color: #f0f0f0;"/>
            
            <!-- Daily Sales Table -->
            <TableView fx:id="dailySalesTable">
               <columns>
                  <TableColumn fx:id="dateColumn" text="التاريخ" prefWidth="120"/>
                  <TableColumn fx:id="dailyInvoicesColumn" text="عدد الفواتير" prefWidth="100"/>
                  <TableColumn fx:id="dailySalesColumn" text="المبيعات" prefWidth="120"/>
                  <TableColumn fx:id="dailyProfitColumn" text="الربح" prefWidth="120"/>
               </columns>
            </TableView>
         </VBox>
      </Tab>
      
      <!-- Top Products Tab -->
      <Tab text="أفضل المنتجات مبيعاً" closable="false">
         <VBox spacing="10">
            <padding>
               <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
            </padding>
            
            <TableView fx:id="topProductsTable">
               <columns>
                  <TableColumn fx:id="productNameColumn" text="اسم المنتج" prefWidth="200"/>
                  <TableColumn fx:id="quantitySoldColumn" text="الكمية المباعة" prefWidth="120"/>
                  <TableColumn fx:id="productSalesColumn" text="إجمالي المبيعات" prefWidth="120"/>
                  <TableColumn fx:id="productProfitColumn" text="الربح" prefWidth="120"/>
               </columns>
            </TableView>
         </VBox>
      </Tab>
      
      <!-- Top Customers Tab -->
      <Tab text="أفضل العملاء" closable="false">
         <VBox spacing="10">
            <padding>
               <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
            </padding>
            
            <TableView fx:id="topCustomersTable">
               <columns>
                  <TableColumn fx:id="customerNameColumn" text="اسم العميل" prefWidth="200"/>
                  <TableColumn fx:id="customerInvoicesColumn" text="عدد الفواتير" prefWidth="120"/>
                  <TableColumn fx:id="customerSalesColumn" text="إجمالي المشتريات" prefWidth="120"/>
                  <TableColumn fx:id="customerLastPurchaseColumn" text="آخر شراء" prefWidth="120"/>
               </columns>
            </TableView>
         </VBox>
      </Tab>
      
      <!-- Payment Methods Tab -->
      <Tab text="طرق الدفع" closable="false">
         <VBox spacing="10">
            <padding>
               <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
            </padding>
            
            <!-- Payment Methods Chart placeholder -->
            <Label text="رسم بياني لطرق الدفع" styleClass="section-title" 
                   style="-fx-alignment: center; -fx-min-height: 200px; -fx-background-color: #f0f0f0;"/>
            
            <TableView fx:id="paymentMethodsTable">
               <columns>
                  <TableColumn fx:id="paymentMethodColumn" text="طريقة الدفع" prefWidth="150"/>
                  <TableColumn fx:id="methodInvoicesColumn" text="عدد الفواتير" prefWidth="120"/>
                  <TableColumn fx:id="methodAmountColumn" text="المبلغ" prefWidth="120"/>
                  <TableColumn fx:id="methodPercentageColumn" text="النسبة %" prefWidth="100"/>
               </columns>
            </TableView>
         </VBox>
      </Tab>
   </TabPane>

   <!-- Status Bar -->
   <HBox alignment="CENTER_LEFT">
      <Label fx:id="statusLabel" text="جاهز لعرض التقرير">
         <font>
            <Font name="Arial" size="11.0" />
         </font>
      </Label>
      
      <Region HBox.hgrow="ALWAYS"/>
      
      <Label fx:id="reportDateLabel" text="">
         <font>
            <Font name="Arial" size="11.0" />
         </font>
      </Label>
   </HBox>

</VBox>
