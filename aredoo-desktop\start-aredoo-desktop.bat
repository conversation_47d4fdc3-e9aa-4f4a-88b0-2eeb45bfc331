@echo off
chcp 65001 > nul
title أريدوو - تطبيق سطح المكتب

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🏪 أريدوو - نظام POS                     ║
echo ║                   Aredoo Desktop POS System                  ║
echo ║                                                              ║
echo ║              نظام إدارة المبيعات والمخزون                   ║
echo ║            Point of Sale and Inventory Management            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

cd /d "%~dp0"

echo 🔍 فحص متطلبات النظام...
echo Checking system requirements...
echo.

REM Check Java
java -version >nul 2>&1
if errorlevel 1 (
    echo ❌ Java غير مثبت أو غير متوفر
    echo ❌ Java is not installed or not available
    echo.
    echo 📥 يرجى تحميل وتثبيت Java 11 أو أحدث من:
    echo 📥 Please download and install Java 11+ from:
    echo    https://adoptium.net/
    echo    https://www.oracle.com/java/technologies/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Java متوفر
echo ✅ Java is available

REM Check JavaFX (basic check)
echo 🔍 فحص JavaFX...
echo 🔍 Checking JavaFX...

REM Create data directory
echo 📁 إنشاء مجلدات البيانات...
echo 📁 Creating data directories...
if not exist "data" mkdir data
if not exist "logs" mkdir logs

echo ✅ تم إعداد البيئة بنجاح
echo ✅ Environment setup completed
echo.

echo 📋 معلومات التطبيق:
echo 📋 Application Information:
echo    • الاسم: أريدوو POS
echo    • الإصدار: 1.0.0
echo    • التقنية: Java + JavaFX + Spring Boot
echo    • قاعدة البيانات: H2 (محلية)
echo.

echo 👤 بيانات الدخول الافتراضية:
echo 👤 Default Login Credentials:
echo    • المدير / Admin: admin / 1234
echo    • الكاشير / Cashier: cashier / 1234
echo.

echo 🚀 بدء تشغيل التطبيق...
echo 🚀 Starting application...
echo.

REM Set JVM options for better Arabic support and performance
set JAVA_OPTS=-Dfile.encoding=UTF-8 -Djava.awt.headless=false -Duser.language=ar -Duser.country=IQ -Xmx512m -Xms256m

REM Try to run the application
echo 💻 تشغيل التطبيق...
echo 💻 Running application...

REM Simple classpath approach (will need proper Maven/Gradle build)
java %JAVA_OPTS% -cp "src\main\java;lib\*" com.aredoo.desktop.AredooDesktopApplication

if errorlevel 1 (
    echo.
    echo ❌ فشل في تشغيل التطبيق
    echo ❌ Failed to start application
    echo.
    echo 🔧 الأسباب المحتملة:
    echo 🔧 Possible causes:
    echo    • JavaFX غير مثبت أو غير متوفر
    echo    • التبعيات مفقودة
    echo    • مشكلة في إعدادات Java
    echo.
    echo 💡 الحلول المقترحة:
    echo 💡 Suggested solutions:
    echo    • تأكد من تثبيت Java 11+ مع JavaFX
    echo    • قم ببناء المشروع باستخدام Maven أو Gradle
    echo    • تحقق من ملفات السجل في مجلد logs
    echo.
    echo 📞 للدعم الفني:
    echo 📞 For technical support:
    echo    • البريد الإلكتروني: <EMAIL>
    echo    • الموقع: www.aredoo.com
    echo.
)

echo.
echo 👋 تم إغلاق التطبيق
echo 👋 Application closed
echo.
echo شكراً لاستخدام أريدوو!
echo Thank you for using Aredoo!
echo.
pause
