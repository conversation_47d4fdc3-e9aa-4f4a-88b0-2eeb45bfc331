@echo off
chcp 65001 > nul
title Aredoo Desktop POS System

echo.
echo ===============================================================
echo                    Aredoo Desktop POS System
echo                   Point of Sale and Inventory Management
echo ===============================================================
echo.

cd /d "%~dp0"

echo Checking system requirements...
echo.

REM Check .NET
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: .NET is not installed or not available
    echo.
    echo Please download and install .NET 9 from:
    echo    https://dotnet.microsoft.com/download
    echo.
    pause
    exit /b 1
)

echo SUCCESS: .NET is available

REM Build the desktop application if needed
echo Building desktop application...
dotnet build AredooDesktop --configuration Release --verbosity quiet

if errorlevel 1 (
    echo ERROR: Failed to build application
    pause
    exit /b 1
)

echo SUCCESS: Application built successfully

REM Build the server application if needed
echo Building server application...
dotnet build Aredoo.Server.csproj --configuration Release --verbosity quiet

if errorlevel 1 (
    echo ERROR: Failed to build server
    pause
    exit /b 1
)

echo SUCCESS: Server built successfully
echo.

echo Application Information:
echo    Name: Aredoo POS
echo    Version: 1.0.0
echo    Technology: .NET 9 + WebView2 + ASP.NET Core
echo    Database: SQLite (local)
echo.

echo Default Login Credentials:
echo    Admin: admin / 1234
echo.

echo Starting application...
echo.

REM Start the desktop application
start "" "AredooDesktop\bin\Release\net9.0-windows\AredooDesktop.exe"

echo Application started successfully!
echo.
echo Note: If the application doesn't appear, check:
echo    - Microsoft Edge WebView2 Runtime installation
echo    - Windows security permissions
echo    - Antivirus settings
echo.

timeout /t 3 /nobreak >nul
