@echo off
chcp 65001 > nul
echo.
echo ========================================
echo    أريدوو - تطبيق سطح المكتب
echo    Aredoo Desktop Application  
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 فحص Java...
echo Checking Java...

java -version >nul 2>&1
if errorlevel 1 (
    echo ❌ Java غير مثبت أو غير موجود في PATH
    echo Java is not installed or not in PATH
    echo.
    echo 📥 يرجى تحميل وتثبيت Java 11 أو أحدث من:
    echo Please download and install Java 11+ from:
    echo https://adoptium.net/
    pause
    exit /b 1
)

echo ✅ Java متوفر
echo Java is available
echo.

echo 📦 إنشاء مجلد البيانات...
echo Creating data directory...
if not exist "data" mkdir data

echo 🚀 تشغيل التطبيق...
echo Starting application...
echo.

echo 📋 بيانات الدخول الافتراضية:
echo Default login credentials:
echo - المدير: admin / 1234
echo - الكاشير: cashier / 1234
echo.

REM Create a simple classpath with just the essentials
set CLASSPATH=src\main\java

REM Try to run with basic classpath
java -Dfile.encoding=UTF-8 -Djava.awt.headless=false -cp "%CLASSPATH%" com.aredoo.desktop.AredooDesktopApplication

if errorlevel 1 (
    echo.
    echo ❌ فشل في تشغيل التطبيق
    echo Failed to start application
    echo.
    echo 💡 يرجى التأكد من:
    echo Please make sure:
    echo - Java 11+ مثبت
    echo - JavaFX متوفر
    echo - جميع التبعيات موجودة
)

echo.
echo 👋 تم إغلاق التطبيق
echo Application closed
pause
