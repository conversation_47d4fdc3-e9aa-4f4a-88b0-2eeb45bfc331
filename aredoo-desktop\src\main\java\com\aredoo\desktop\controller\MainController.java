package com.aredoo.desktop.controller;

import com.aredoo.desktop.service.UserSession;
import javafx.animation.KeyFrame;
import javafx.animation.Timeline;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.util.Duration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Controller
public class MainController {

    @FXML private TabPane mainTabPane;
    @FXML private Label userLabel;
    @FXML private Label dateTimeLabel;
    @FXML private Label totalSalesLabel;
    @FXML private Label totalProductsLabel;
    @FXML private Label totalCustomersLabel;
    @FXML private Label lowStockLabel;

    @Autowired
    private UserSession userSession;

    private Timeline clockTimeline;

    @FXML
    private void initialize() {
        setupUserInfo();
        setupClock();
        loadDashboardData();
    }

    private void setupUserInfo() {
        if (userSession.isLoggedIn()) {
            userLabel.setText("مرحباً، " + userSession.getCurrentUserName() + " (" + userSession.getCurrentUserRole() + ")");
        }
    }

    private void setupClock() {
        clockTimeline = new Timeline(new KeyFrame(Duration.seconds(1), e -> updateDateTime()));
        clockTimeline.setCycleCount(Timeline.INDEFINITE);
        clockTimeline.play();
        updateDateTime();
    }

    private void updateDateTime() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd - HH:mm:ss");
        dateTimeLabel.setText(now.format(formatter));
    }

    private void loadDashboardData() {
        // TODO: Load actual data from database
        totalSalesLabel.setText("0 د.ع");
        totalProductsLabel.setText("0");
        totalCustomersLabel.setText("0");
        lowStockLabel.setText("0");
    }

    @FXML
    private void newSaleInvoice() {
        openWindow("/fxml/invoice-form.fxml", "فاتورة مبيعات جديدة", 800, 600);
    }

    @FXML
    private void showProducts() {
        addTab("المنتجات", "/fxml/products.fxml");
    }

    @FXML
    private void addProduct() {
        openWindow("/fxml/product-form.fxml", "إضافة منتج جديد", 600, 500);
    }

    @FXML
    private void showCategories() {
        addTab("التصنيفات", "/fxml/categories.fxml");
    }

    @FXML
    private void showInvoices() {
        addTab("الفواتير", "/fxml/invoices.fxml");
    }

    @FXML
    private void newPurchaseInvoice() {
        openWindow("/fxml/purchase-form.fxml", "فاتورة شراء جديدة", 800, 600);
    }

    @FXML
    private void showSuppliers() {
        addTab("الموردين", "/fxml/suppliers.fxml");
    }

    @FXML
    private void showCustomers() {
        addTab("العملاء", "/fxml/customers.fxml");
    }

    @FXML
    private void addCustomer() {
        openWindow("/fxml/customer-form.fxml", "إضافة عميل جديد", 500, 400);
    }

    @FXML
    private void salesReport() {
        addTab("تقرير المبيعات", "/fxml/sales-report.fxml");
    }

    @FXML
    private void inventoryReport() {
        addTab("تقرير المخزون", "/fxml/inventory-report.fxml");
    }

    @FXML
    private void expensesReport() {
        addTab("تقرير المصروفات", "/fxml/expenses-report.fxml");
    }

    @FXML
    private void openSettings() {
        openWindow("/fxml/settings.fxml", "الإعدادات", 600, 500);
    }

    @FXML
    private void about() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("حول البرنامج");
        alert.setHeaderText("أريدوو - نظام إدارة المبيعات والمخزون");
        alert.setContentText("الإصدار: 1.0.0\nتطوير: فريق أريدوو\nتطبيق سطح مكتب بتقنية JavaFX");
        alert.showAndWait();
    }

    @FXML
    private void logout() {
        if (clockTimeline != null) {
            clockTimeline.stop();
        }
        
        userSession.logout();
        
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/login.fxml"));
            Parent root = loader.load();
            Scene scene = new Scene(root, 400, 300);
            scene.getStylesheets().add(getClass().getResource("/css/arabic-style.css").toExternalForm());
            
            Stage stage = (Stage) mainTabPane.getScene().getWindow();
            stage.setScene(scene);
            stage.setResizable(false);
            stage.centerOnScreen();
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @FXML
    private void exit() {
        if (clockTimeline != null) {
            clockTimeline.stop();
        }
        Platform.exit();
    }

    private void addTab(String title, String fxmlPath) {
        // Check if tab already exists
        for (Tab tab : mainTabPane.getTabs()) {
            if (title.equals(tab.getText())) {
                mainTabPane.getSelectionModel().select(tab);
                return;
            }
        }

        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource(fxmlPath));
            Parent content = loader.load();
            
            Tab tab = new Tab(title);
            tab.setContent(content);
            tab.setClosable(true);
            
            mainTabPane.getTabs().add(tab);
            mainTabPane.getSelectionModel().select(tab);
            
        } catch (Exception e) {
            showError("خطأ في فتح النافذة: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void openWindow(String fxmlPath, String title, int width, int height) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource(fxmlPath));
            Parent root = loader.load();
            
            Stage stage = new Stage();
            stage.setTitle(title);
            stage.setScene(new Scene(root, width, height));
            stage.initModality(Modality.APPLICATION_MODAL);
            stage.centerOnScreen();
            stage.show();
            
        } catch (Exception e) {
            showError("خطأ في فتح النافذة: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void showError(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("خطأ");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
