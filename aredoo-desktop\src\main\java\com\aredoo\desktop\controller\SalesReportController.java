package com.aredoo.desktop.controller;

import com.aredoo.desktop.repository.InvoiceRepository;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Controller
public class SalesReportController {

    @FXML private DatePicker fromDatePicker;
    @FXML private DatePicker toDatePicker;
    @FXML private Label totalSalesLabel;
    @FXML private Label totalInvoicesLabel;
    @FXML private Label averageInvoiceLabel;
    @FXML private Label totalPaidLabel;
    @FXML private Label totalRemainingLabel;
    @FXML private Label netProfitLabel;
    @FXML private TableView<?> dailySalesTable;
    @FXML private TableView<?> topProductsTable;
    @FXML private TableView<?> topCustomersTable;
    @FXML private TableView<?> paymentMethodsTable;
    @FXML private Label statusLabel;
    @FXML private Label reportDateLabel;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @FXML
    private void initialize() {
        setupDatePickers();
        setThisMonth(); // Default to current month
    }

    private void setupDatePickers() {
        // Set default dates
        LocalDate now = LocalDate.now();
        fromDatePicker.setValue(now.withDayOfMonth(1)); // First day of month
        toDatePicker.setValue(now); // Today
    }

    @FXML
    private void generateReport() {
        LocalDate fromDate = fromDatePicker.getValue();
        LocalDate toDate = toDatePicker.getValue();

        if (fromDate == null || toDate == null) {
            showWarning("يرجى اختيار الفترة الزمنية");
            return;
        }

        if (fromDate.isAfter(toDate)) {
            showWarning("تاريخ البداية يجب أن يكون قبل تاريخ النهاية");
            return;
        }

        try {
            updateStatus("جاري إنشاء التقرير...");
            
            // Load report data
            loadSummaryData(fromDate, toDate);
            loadDailySalesData(fromDate, toDate);
            loadTopProductsData(fromDate, toDate);
            loadTopCustomersData(fromDate, toDate);
            loadPaymentMethodsData(fromDate, toDate);
            
            // Update report date label
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
            reportDateLabel.setText("من " + fromDate.format(formatter) + " إلى " + toDate.format(formatter));
            
            updateStatus("تم إنشاء التقرير بنجاح");
            
        } catch (Exception e) {
            showError("خطأ في إنشاء التقرير: " + e.getMessage());
            updateStatus("خطأ في إنشاء التقرير");
        }
    }

    private void loadSummaryData(LocalDate fromDate, LocalDate toDate) {
        // TODO: Implement actual data loading from database
        // For now, show placeholder data
        totalSalesLabel.setText("0.00 د.ع");
        totalInvoicesLabel.setText("0");
        averageInvoiceLabel.setText("0.00 د.ع");
        totalPaidLabel.setText("0.00 د.ع");
        totalRemainingLabel.setText("0.00 د.ع");
        netProfitLabel.setText("0.00 د.ع");
    }

    private void loadDailySalesData(LocalDate fromDate, LocalDate toDate) {
        // TODO: Implement daily sales data loading
    }

    private void loadTopProductsData(LocalDate fromDate, LocalDate toDate) {
        // TODO: Implement top products data loading
    }

    private void loadTopCustomersData(LocalDate fromDate, LocalDate toDate) {
        // TODO: Implement top customers data loading
    }

    private void loadPaymentMethodsData(LocalDate fromDate, LocalDate toDate) {
        // TODO: Implement payment methods data loading
    }

    @FXML
    private void setToday() {
        LocalDate today = LocalDate.now();
        fromDatePicker.setValue(today);
        toDatePicker.setValue(today);
        generateReport();
    }

    @FXML
    private void setThisWeek() {
        LocalDate today = LocalDate.now();
        LocalDate startOfWeek = today.minusDays(today.getDayOfWeek().getValue() - 1);
        fromDatePicker.setValue(startOfWeek);
        toDatePicker.setValue(today);
        generateReport();
    }

    @FXML
    private void setThisMonth() {
        LocalDate today = LocalDate.now();
        fromDatePicker.setValue(today.withDayOfMonth(1));
        toDatePicker.setValue(today);
        generateReport();
    }

    @FXML
    private void exportToPDF() {
        // TODO: Implement PDF export
        showInfo("سيتم تنفيذ تصدير PDF قريباً");
    }

    @FXML
    private void printReport() {
        // TODO: Implement printing
        showInfo("سيتم تنفيذ الطباعة قريباً");
    }

    private void updateStatus(String message) {
        statusLabel.setText(message);
    }

    private void showError(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("خطأ");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showWarning(String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("تحذير");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showInfo(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("معلومات");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
