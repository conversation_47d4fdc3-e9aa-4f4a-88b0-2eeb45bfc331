@echo off
chcp 65001 > nul
title Aredoo Desktop POS System

echo.
echo ===============================================================
echo                    Aredoo Desktop POS System                  
echo                   Point of Sale and Inventory Management     
echo                        C# Windows Forms Application
echo ===============================================================
echo.

cd /d "%~dp0"

echo Checking system requirements...
echo.

REM Check .NET
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: .NET is not installed or not available
    echo.
    echo Please download and install .NET 9 from:
    echo    https://dotnet.microsoft.com/download
    echo.
    pause
    exit /b 1
)

echo SUCCESS: .NET 9 is available

REM Build the desktop application
echo Building desktop application...
dotnet build AredooDesktopApp --configuration Release --verbosity quiet

if errorlevel 1 (
    echo ERROR: Failed to build desktop application
    pause
    exit /b 1
)

echo SUCCESS: Desktop application built successfully
echo.

echo Application Information:
echo    Name: Aredoo Desktop POS
echo    Version: 1.0.0
echo    Technology: C# Windows Forms + .NET 9
echo    Database: SQLite (local)
echo    Features: Product Management, Inventory Control
echo.

echo Default Login Credentials:
echo    Username: admin
echo    Password: 1234
echo.

echo Starting desktop application...
echo.

REM Start the desktop application
start "" "AredooDesktopApp\bin\Release\net9.0-windows\AredooDesktopApp.exe"

if errorlevel 1 (
    echo ERROR: Failed to start desktop application
    echo Trying alternative method...
    dotnet run --project AredooDesktopApp --configuration Release
)

echo.
echo SUCCESS: Desktop application started!
echo.
echo The application should now be running in a separate window.
echo You can close this command window safely.
echo.

pause
