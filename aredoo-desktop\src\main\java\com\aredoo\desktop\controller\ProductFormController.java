package com.aredoo.desktop.controller;

import com.aredoo.desktop.model.Product;
import com.aredoo.desktop.repository.ProductRepository;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.stage.Stage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Controller
public class ProductFormController {

    @FXML private Label formTitle;
    @FXML private TextField codeField;
    @FXML private TextField nameField;
    @FXML private TextField nameArField;
    @FXML private ComboBox<String> categoryComboBox;
    @FXML private ComboBox<String> unitComboBox;
    @FXML private TextField quantityField;
    @FXML private TextField purchasePriceField;
    @FXML private TextField salePriceField;
    @FXML private TextField minStockField;
    @FXML private TextField maxStockField;
    @FXML private TextArea descriptionArea;
    @FXML private CheckBox isActiveCheckBox;
    @FXML private Label errorLabel;

    @Autowired
    private ProductRepository productRepository;

    private Product currentProduct;
    private boolean isEditMode = false;

    @FXML
    private void initialize() {
        loadCategories();
        loadUnits();
        setupValidation();
    }

    private void loadCategories() {
        try {
            List<String> categories = productRepository.findAllCategories();
            categoryComboBox.getItems().addAll(categories);
        } catch (Exception e) {
            System.err.println("خطأ في تحميل التصنيفات: " + e.getMessage());
        }
    }

    private void loadUnits() {
        unitComboBox.getItems().addAll(
            "قطعة", "كيلو", "جرام", "لتر", "متر", "علبة", "كرتون", "دزينة"
        );
    }

    private void setupValidation() {
        // Allow only numbers in quantity and price fields
        quantityField.textProperty().addListener((observable, oldValue, newValue) -> {
            if (!newValue.matches("\\d*")) {
                quantityField.setText(newValue.replaceAll("[^\\d]", ""));
            }
        });

        purchasePriceField.textProperty().addListener((observable, oldValue, newValue) -> {
            if (!newValue.matches("\\d*\\.?\\d*")) {
                purchasePriceField.setText(oldValue);
            }
        });

        salePriceField.textProperty().addListener((observable, oldValue, newValue) -> {
            if (!newValue.matches("\\d*\\.?\\d*")) {
                salePriceField.setText(oldValue);
            }
        });

        minStockField.textProperty().addListener((observable, oldValue, newValue) -> {
            if (!newValue.matches("\\d*")) {
                minStockField.setText(newValue.replaceAll("[^\\d]", ""));
            }
        });

        maxStockField.textProperty().addListener((observable, oldValue, newValue) -> {
            if (!newValue.matches("\\d*")) {
                maxStockField.setText(newValue.replaceAll("[^\\d]", ""));
            }
        });
    }

    public void setProduct(Product product) {
        this.currentProduct = product;
        this.isEditMode = true;
        
        formTitle.setText("تعديل المنتج");
        
        // Fill form with product data
        codeField.setText(product.getCode());
        nameField.setText(product.getName());
        nameArField.setText(product.getNameAr());
        categoryComboBox.setValue(product.getCategory());
        unitComboBox.setValue(product.getUnit());
        quantityField.setText(String.valueOf(product.getQuantity()));
        purchasePriceField.setText(product.getPurchasePrice().toString());
        salePriceField.setText(product.getSalePrice().toString());
        minStockField.setText(String.valueOf(product.getMinStock()));
        maxStockField.setText(String.valueOf(product.getMaxStock()));
        descriptionArea.setText(product.getDescription());
        isActiveCheckBox.setSelected(product.getIsActive());
    }

    @FXML
    private void saveProduct() {
        if (!validateForm()) {
            return;
        }

        try {
            Product product = isEditMode ? currentProduct : new Product();
            
            // Set product data
            product.setCode(codeField.getText().trim());
            product.setName(nameField.getText().trim());
            product.setNameAr(nameArField.getText().trim());
            product.setCategory(categoryComboBox.getValue());
            product.setUnit(unitComboBox.getValue());
            product.setQuantity(Integer.parseInt(quantityField.getText().trim()));
            product.setPurchasePrice(new BigDecimal(purchasePriceField.getText().trim()));
            product.setSalePrice(new BigDecimal(salePriceField.getText().trim()));
            product.setMinStock(Integer.parseInt(minStockField.getText().trim()));
            product.setMaxStock(Integer.parseInt(maxStockField.getText().trim()));
            product.setDescription(descriptionArea.getText().trim());
            product.setIsActive(isActiveCheckBox.isSelected());
            
            if (!isEditMode) {
                product.setCreatedAt(LocalDateTime.now());
            }
            product.setUpdatedAt(LocalDateTime.now());

            // Save product
            productRepository.save(product);
            
            // Show success message
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("نجح الحفظ");
            alert.setHeaderText(null);
            alert.setContentText(isEditMode ? "تم تحديث المنتج بنجاح" : "تم إضافة المنتج بنجاح");
            alert.showAndWait();
            
            // Close window
            closeWindow();
            
        } catch (Exception e) {
            showError("خطأ في حفظ المنتج: " + e.getMessage());
        }
    }

    @FXML
    private void cancel() {
        closeWindow();
    }

    private boolean validateForm() {
        StringBuilder errors = new StringBuilder();

        if (codeField.getText().trim().isEmpty()) {
            errors.append("• كود المنتج مطلوب\n");
        }

        if (nameField.getText().trim().isEmpty()) {
            errors.append("• اسم المنتج (إنجليزي) مطلوب\n");
        }

        if (nameArField.getText().trim().isEmpty()) {
            errors.append("• اسم المنتج (عربي) مطلوب\n");
        }

        if (categoryComboBox.getValue() == null || categoryComboBox.getValue().trim().isEmpty()) {
            errors.append("• التصنيف مطلوب\n");
        }

        if (unitComboBox.getValue() == null || unitComboBox.getValue().trim().isEmpty()) {
            errors.append("• الوحدة مطلوبة\n");
        }

        if (purchasePriceField.getText().trim().isEmpty()) {
            errors.append("• سعر الشراء مطلوب\n");
        }

        if (salePriceField.getText().trim().isEmpty()) {
            errors.append("• سعر البيع مطلوب\n");
        }

        if (errors.length() > 0) {
            showError("يرجى تصحيح الأخطاء التالية:\n" + errors.toString());
            return false;
        }

        // Check if code already exists (for new products)
        if (!isEditMode) {
            try {
                if (productRepository.findByCode(codeField.getText().trim()).isPresent()) {
                    showError("كود المنتج موجود مسبقاً");
                    return false;
                }
            } catch (Exception e) {
                showError("خطأ في التحقق من كود المنتج");
                return false;
            }
        }

        return true;
    }

    private void showError(String message) {
        errorLabel.setText(message);
        errorLabel.setVisible(true);
    }

    private void closeWindow() {
        Stage stage = (Stage) codeField.getScene().getWindow();
        stage.close();
    }
}
