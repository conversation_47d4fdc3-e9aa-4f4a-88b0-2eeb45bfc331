# Server Configuration (disabled for desktop app)
server.port=0
server.servlet.context-path=/

# Database Configuration (H2 Embedded)
spring.datasource.url=jdbc:h2:file:./data/aredoo-desktop;DB_CLOSE_ON_EXIT=FALSE;AUTO_RECONNECT=TRUE;MODE=MySQL;DATABASE_TO_LOWER=TRUE;CASE_INSENSITIVE_IDENTIFIERS=TRUE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=2

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=false
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true

# H2 Console (disabled for desktop app)
spring.h2.console.enabled=false

# Logging Configuration
logging.level.com.aredoo.desktop=INFO
logging.level.org.springframework=WARN
logging.level.org.hibernate=WARN
logging.level.org.h2=WARN
logging.file.name=logs/aredoo-desktop.log
logging.file.max-size=10MB
logging.file.max-history=30

# Application Properties
app.name=أريدوو - نظام إدارة المبيعات والمخزون
app.name.en=Aredoo POS System
app.version=1.0.0
app.author=Aredoo Team
app.description=نظام إدارة المبيعات والمخزون للشركات العربية
app.website=www.aredoo.com
app.support.email=<EMAIL>

# Internationalization
spring.messages.basename=messages
spring.messages.encoding=UTF-8
spring.messages.fallback-to-system-locale=false

# JavaFX Properties
javafx.application.name=${app.name}
javafx.application.version=${app.version}

# Security Configuration
app.security.password.min-length=4
app.security.session.timeout=3600

# Business Configuration
app.business.currency=IQD
app.business.currency.symbol=د.ع
app.business.tax.default=0
app.business.discount.max=100

# Backup Configuration
app.backup.auto.enabled=true
app.backup.auto.interval=24
app.backup.location=./backups
