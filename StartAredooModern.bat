@echo off
chcp 65001 > nul
title 🏪 Aredoo Modern POS System

color 0B
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                          🏪 أريدوو - نظام نقاط البيع الحديث                    ║
echo ║                         Aredoo Modern POS System                            ║
echo ╠══════════════════════════════════════════════════════════════════════════════╣
echo ║  📦 إدارة المنتجات والمخزون                                                   ║
echo ║  💰 نظام المبيعات المتقدم                                                     ║
echo ║  📊 التقارير والإحصائيات                                                      ║
echo ║  🎨 واجهة حديثة وسهلة الاستخدام                                               ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

cd /d "%~dp0"

echo 🔍 فحص متطلبات النظام...
echo.

REM Check .NET
dotnet --version >nul 2>&1
if errorlevel 1 (
    color 0C
    echo ❌ خطأ: .NET 9 غير مثبت
    echo.
    echo 📥 يرجى تحميل وتثبيت .NET 9 من:
    echo    🌐 https://dotnet.microsoft.com/download
    echo.
    echo 💡 بعد التثبيت، قم بتشغيل هذا الملف مرة أخرى
    echo.
    pause
    exit /b 1
) else (
    color 0A
    echo ✅ .NET 9 مثبت ومتاح
)

echo.
echo 🔨 بناء التطبيق...
echo.

dotnet build AredooDesktopApp --configuration Release --verbosity minimal

if errorlevel 1 (
    color 0C
    echo ❌ فشل في بناء التطبيق
    echo.
    echo 🔧 يرجى فحص رسائل الخطأ أعلاه والمحاولة مرة أخرى
    pause
    exit /b 1
) else (
    color 0A
    echo ✅ تم بناء التطبيق بنجاح
)

echo.
color 0E
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                              معلومات التطبيق                                ║
echo ╠══════════════════════════════════════════════════════════════════════════════╣
echo ║  📱 الاسم: أريدوو - نظام نقاط البيع الحديث                                   ║
echo ║  🔢 الإصدار: 2.0.0 - Enhanced Edition                                      ║
echo ║  💻 التقنية: C# + Windows Forms + .NET 9                                   ║
echo ║  🗄️ قاعدة البيانات: SQLite (محلية)                                          ║
echo ║  🎨 التصميم: Modern Material Design                                         ║
echo ╠══════════════════════════════════════════════════════════════════════════════╣
echo ║                              المميزات الجديدة                               ║
echo ╠══════════════════════════════════════════════════════════════════════════════╣
echo ║  ✨ واجهة حديثة مع تأثيرات بصرية                                            ║
echo ║  🎨 ألوان متدرجة وأزرار ثلاثية الأبعاد                                      ║
echo ║  🔍 بحث محسن مع تأثيرات تفاعلية                                             ║
echo ║  📊 إحصائيات مباشرة في الواجهة الرئيسية                                     ║
echo ║  ⚡ أداء محسن وسرعة استجابة عالية                                           ║
echo ║  🌐 دعم كامل للغة العربية مع RTL                                            ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

color 0D
echo 🔐 بيانات الدخول الافتراضية:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: 1234
echo.

color 0B
echo 🚀 تشغيل التطبيق...
echo.

REM Start the application
start "" "AredooDesktopApp\bin\Release\net9.0-windows\AredooDesktopApp.exe"

if errorlevel 1 (
    color 0C
    echo ❌ فشل في تشغيل التطبيق
    echo 🔄 محاولة طريقة بديلة...
    dotnet run --project AredooDesktopApp --configuration Release
    
    if errorlevel 1 (
        echo ❌ فشل في تشغيل التطبيق بالطريقة البديلة
        echo.
        echo 💡 نصائح لحل المشكلة:
        echo    🛡️ تحقق من إعدادات Windows Defender
        echo    🔒 تحقق من إعدادات مكافح الفيروسات
        echo    👨‍💼 جرب تشغيل الملف كمدير
        echo.
        pause
        exit /b 1
    )
)

color 0A
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                            🎉 تم تشغيل التطبيق بنجاح!                        ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo 💡 التطبيق يعمل الآن في نافذة منفصلة
echo 🔄 يمكنك إغلاق هذه النافذة بأمان
echo.
echo 📞 للدعم الفني أو الاستفسارات:
echo    📧 البريد الإلكتروني: <EMAIL>
echo    🌐 الموقع الإلكتروني: www.aredoo.com
echo.

timeout /t 5 /nobreak >nul

color 0F
echo شكراً لاستخدام أريدوو! 🙏
echo.
pause
