<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" 
           fx:controller="com.aredoo.desktop.controller.MainController"
           styleClass="main-container">

   <!-- Top Menu Bar -->
   <top>
      <VBox>
         <!-- Menu Bar -->
         <MenuBar styleClass="menu-bar">
            <Menu text="الملف">
               <MenuItem text="إعدادات" onAction="#openSettings"/>
               <SeparatorMenuItem />
               <MenuItem text="تسجيل الخروج" onAction="#logout"/>
               <MenuItem text="خروج" onAction="#exit"/>
            </Menu>
            
            <Menu text="المنتجات">
               <MenuItem text="عرض المنتجات" onAction="#showProducts"/>
               <MenuItem text="إضافة منتج" onAction="#addProduct"/>
               <MenuItem text="التصنيفات" onAction="#showCategories"/>
            </Menu>
            
            <Menu text="المبيعات">
               <MenuItem text="فاتورة جديدة" onAction="#newSaleInvoice"/>
               <MenuItem text="عرض الفواتير" onAction="#showInvoices"/>
            </Menu>
            
            <Menu text="المشتريات">
               <MenuItem text="فاتورة شراء" onAction="#newPurchaseInvoice"/>
               <MenuItem text="الموردين" onAction="#showSuppliers"/>
            </Menu>
            
            <Menu text="العملاء">
               <MenuItem text="عرض العملاء" onAction="#showCustomers"/>
               <MenuItem text="إضافة عميل" onAction="#addCustomer"/>
            </Menu>
            
            <Menu text="التقارير">
               <MenuItem text="تقرير المبيعات" onAction="#salesReport"/>
               <MenuItem text="تقرير المخزون" onAction="#inventoryReport"/>
               <MenuItem text="تقرير المصروفات" onAction="#expensesReport"/>
            </Menu>
            
            <Menu text="المساعدة">
               <MenuItem text="حول البرنامج" onAction="#about"/>
            </Menu>
         </MenuBar>
         
         <!-- Toolbar -->
         <ToolBar styleClass="toolbar">
            <Button text="فاتورة جديدة" onAction="#newSaleInvoice" styleClass="toolbar-button"/>
            <Separator orientation="VERTICAL"/>
            <Button text="المنتجات" onAction="#showProducts" styleClass="toolbar-button"/>
            <Button text="العملاء" onAction="#showCustomers" styleClass="toolbar-button"/>
            <Separator orientation="VERTICAL"/>
            <Button text="التقارير" onAction="#salesReport" styleClass="toolbar-button"/>
            
            <Region HBox.hgrow="ALWAYS"/>
            
            <Label fx:id="userLabel" text="مرحباً، " styleClass="user-label">
               <font>
                  <Font name="Arial" size="12.0" />
               </font>
            </Label>
         </ToolBar>
      </VBox>
   </top>

   <!-- Main Content Area -->
   <center>
      <TabPane fx:id="mainTabPane" styleClass="main-tab-pane" tabClosingPolicy="SELECTED_TAB">
         <!-- Dashboard Tab -->
         <Tab text="لوحة التحكم" closable="false">
            <ScrollPane fitToWidth="true" fitToHeight="true">
               <VBox spacing="20" styleClass="dashboard-container">
                  <padding>
                     <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                  </padding>
                  
                  <!-- Welcome Section -->
                  <VBox spacing="10" styleClass="welcome-section">
                     <Label text="مرحباً بك في نظام أريدوو" styleClass="welcome-title">
                        <font>
                           <Font name="Arial" size="24.0" />
                        </font>
                     </Label>
                     <Label text="نظام إدارة المبيعات والمخزون" styleClass="welcome-subtitle">
                        <font>
                           <Font name="Arial" size="14.0" />
                        </font>
                     </Label>
                  </VBox>
                  
                  <!-- Statistics Cards -->
                  <GridPane hgap="20" vgap="20" styleClass="stats-grid">
                     <!-- Sales Card -->
                     <VBox styleClass="stat-card" GridPane.columnIndex="0" GridPane.rowIndex="0">
                        <Label text="إجمالي المبيعات" styleClass="stat-title"/>
                        <Label fx:id="totalSalesLabel" text="0 د.ع" styleClass="stat-value"/>
                        <Label text="اليوم" styleClass="stat-subtitle"/>
                     </VBox>
                     
                     <!-- Products Card -->
                     <VBox styleClass="stat-card" GridPane.columnIndex="1" GridPane.rowIndex="0">
                        <Label text="عدد المنتجات" styleClass="stat-title"/>
                        <Label fx:id="totalProductsLabel" text="0" styleClass="stat-value"/>
                        <Label text="منتج نشط" styleClass="stat-subtitle"/>
                     </VBox>
                     
                     <!-- Customers Card -->
                     <VBox styleClass="stat-card" GridPane.columnIndex="2" GridPane.rowIndex="0">
                        <Label text="العملاء" styleClass="stat-title"/>
                        <Label fx:id="totalCustomersLabel" text="0" styleClass="stat-value"/>
                        <Label text="عميل نشط" styleClass="stat-subtitle"/>
                     </VBox>
                     
                     <!-- Low Stock Card -->
                     <VBox styleClass="stat-card alert-card" GridPane.columnIndex="0" GridPane.rowIndex="1">
                        <Label text="تنبيه المخزون" styleClass="stat-title"/>
                        <Label fx:id="lowStockLabel" text="0" styleClass="stat-value"/>
                        <Label text="منتج ينفد" styleClass="stat-subtitle"/>
                     </VBox>
                  </GridPane>
                  
                  <!-- Quick Actions -->
                  <VBox spacing="15" styleClass="quick-actions">
                     <Label text="الإجراءات السريعة" styleClass="section-title">
                        <font>
                           <Font name="Arial" size="16.0" />
                        </font>
                     </Label>
                     
                     <HBox spacing="15">
                        <Button text="فاتورة مبيعات جديدة" onAction="#newSaleInvoice" 
                               styleClass="quick-action-button" prefWidth="200"/>
                        <Button text="إضافة منتج جديد" onAction="#addProduct" 
                               styleClass="quick-action-button" prefWidth="200"/>
                        <Button text="إضافة عميل جديد" onAction="#addCustomer" 
                               styleClass="quick-action-button" prefWidth="200"/>
                     </HBox>
                  </VBox>
               </VBox>
            </ScrollPane>
         </Tab>
      </TabPane>
   </center>

   <!-- Status Bar -->
   <bottom>
      <HBox styleClass="status-bar" spacing="10">
         <padding>
            <Insets bottom="5.0" left="10.0" right="10.0" top="5.0" />
         </padding>
         
         <Label text="جاهز" styleClass="status-label"/>
         <Region HBox.hgrow="ALWAYS"/>
         <Label fx:id="dateTimeLabel" styleClass="datetime-label"/>
      </HBox>
   </bottom>

</BorderPane>
