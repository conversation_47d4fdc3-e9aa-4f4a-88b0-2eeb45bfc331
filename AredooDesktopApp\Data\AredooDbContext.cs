using Microsoft.EntityFrameworkCore;
using AredooDesktopApp.Models;

namespace AredooDesktopApp.Data;

public class AredooDbContext : DbContext
{
    public DbSet<Product> Products { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<Category> Categories { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "aredoo.db");
        Directory.CreateDirectory(Path.GetDirectoryName(dbPath)!);
        
        optionsBuilder.UseSqlite($"Data Source={dbPath}");
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Product configuration
        modelBuilder.Entity<Product>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.Code).IsUnique();
            entity.HasIndex(e => e.Barcode).IsUnique();
            entity.Property(e => e.PurchasePrice).HasPrecision(18, 2);
            entity.Property(e => e.SalePrice).HasPrecision(18, 2);
            entity.Property(e => e.WholesalePrice).HasPrecision(18, 2);
        });

        // User configuration
        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.Username).IsUnique();
        });

        // Category configuration
        modelBuilder.Entity<Category>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.Name).IsUnique();
        });

        // Seed data
        SeedData(modelBuilder);
    }

    private void SeedData(ModelBuilder modelBuilder)
    {
        // Seed default admin user
        modelBuilder.Entity<User>().HasData(
            new User
            {
                Id = 1,
                Username = "admin",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("1234"),
                FullName = "مدير النظام",
                Role = "Admin",
                IsActive = true,
                CreatedAt = DateTime.Now
            }
        );

        // Seed default categories
        modelBuilder.Entity<Category>().HasData(
            new Category { Id = 1, Name = "General", NameAr = "عام", Description = "فئة عامة", IsActive = true, CreatedAt = DateTime.Now },
            new Category { Id = 2, Name = "Electronics", NameAr = "إلكترونيات", Description = "الأجهزة الإلكترونية", IsActive = true, CreatedAt = DateTime.Now },
            new Category { Id = 3, Name = "Clothing", NameAr = "ملابس", Description = "الملابس والأزياء", IsActive = true, CreatedAt = DateTime.Now },
            new Category { Id = 4, Name = "Food", NameAr = "طعام", Description = "المواد الغذائية", IsActive = true, CreatedAt = DateTime.Now }
        );

        // Seed sample products
        modelBuilder.Entity<Product>().HasData(
            new Product
            {
                Id = 1,
                Code = "P001",
                Name = "Sample Product 1",
                NameAr = "منتج تجريبي 1",
                Category = "General",
                Unit = "قطعة",
                PurchasePrice = 10.00m,
                SalePrice = 15.00m,
                WholesalePrice = 12.00m,
                Quantity = 100,
                MinQuantity = 10,
                IsActive = true,
                CreatedAt = DateTime.Now
            },
            new Product
            {
                Id = 2,
                Code = "P002",
                Name = "Sample Product 2",
                NameAr = "منتج تجريبي 2",
                Category = "Electronics",
                Unit = "قطعة",
                PurchasePrice = 50.00m,
                SalePrice = 75.00m,
                WholesalePrice = 60.00m,
                Quantity = 50,
                MinQuantity = 5,
                IsActive = true,
                CreatedAt = DateTime.Now
            }
        );
    }
}
