<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" 
      fx:controller="com.aredoo.desktop.controller.ProductsController"
      spacing="10" styleClass="form-container">

   <padding>
      <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
   </padding>

   <!-- Header -->
   <HBox alignment="CENTER_LEFT" spacing="10">
      <Label text="إدارة المنتجات" styleClass="section-title">
         <font>
            <Font name="Arial" size="18.0" />
         </font>
      </Label>
      
      <Region HBox.hgrow="ALWAYS"/>
      
      <Button text="إضافة منتج جديد" onAction="#addNewProduct" 
              styleClass="quick-action-button" prefWidth="150"/>
   </HBox>

   <!-- Search and Filter -->
   <HBox spacing="10" alignment="CENTER_LEFT">
      <Label text="البحث:">
         <font>
            <Font name="Arial" size="12.0" />
         </font>
      </Label>
      <TextField fx:id="searchField" promptText="ابحث بالاسم أو الكود..." 
                prefWidth="200" styleClass="text-field-arabic"/>
      
      <Label text="التصنيف:">
         <font>
            <Font name="Arial" size="12.0" />
         </font>
      </Label>
      <ComboBox fx:id="categoryComboBox" prefWidth="150" promptText="جميع التصنيفات"/>
      
      <Button text="بحث" onAction="#searchProducts" styleClass="toolbar-button"/>
      <Button text="إعادة تعيين" onAction="#resetSearch" styleClass="button"/>
   </HBox>

   <!-- Products Table -->
   <TableView fx:id="productsTable" VBox.vgrow="ALWAYS">
      <columns>
         <TableColumn fx:id="codeColumn" text="الكود" prefWidth="100"/>
         <TableColumn fx:id="nameColumn" text="اسم المنتج" prefWidth="200"/>
         <TableColumn fx:id="nameArColumn" text="الاسم العربي" prefWidth="200"/>
         <TableColumn fx:id="categoryColumn" text="التصنيف" prefWidth="120"/>
         <TableColumn fx:id="unitColumn" text="الوحدة" prefWidth="80"/>
         <TableColumn fx:id="purchasePriceColumn" text="سعر الشراء" prefWidth="100"/>
         <TableColumn fx:id="salePriceColumn" text="سعر البيع" prefWidth="100"/>
         <TableColumn fx:id="quantityColumn" text="الكمية" prefWidth="80"/>
         <TableColumn fx:id="actionsColumn" text="الإجراءات" prefWidth="150"/>
      </columns>
   </TableView>

   <!-- Bottom Actions -->
   <HBox spacing="10" alignment="CENTER_LEFT">
      <Button text="تعديل" onAction="#editSelectedProduct" 
              styleClass="toolbar-button" prefWidth="80"/>
      <Button text="حذف" onAction="#deleteSelectedProduct" 
              styleClass="button" prefWidth="80"/>
      <Button text="تصدير إلى Excel" onAction="#exportToExcel" 
              styleClass="button" prefWidth="120"/>
      
      <Region HBox.hgrow="ALWAYS"/>
      
      <Label fx:id="statusLabel" text="جاهز">
         <font>
            <Font name="Arial" size="11.0" />
         </font>
      </Label>
   </HBox>

</VBox>
